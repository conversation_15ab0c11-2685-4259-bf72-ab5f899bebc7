import { Activity } from '@quarterback/types';

export default function activitySource(activity: Activity): string {
    switch (activity?.type) {
        case 'asx-announcement':
            return 'ASX';
        case 'hotcopper':
            return 'Hotcopper';
        case 'reddit':
        case 'redditComment':
            return `Reddit`;
        case 'linkedIn':
            return 'LinkedIn'
        case 'tweet':
            return 'Twitter';
        default:
            return "Other";
    }
}