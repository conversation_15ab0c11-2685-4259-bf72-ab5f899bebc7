import { Activity } from '@quarterback/types';

export function author(activity: Activity) {
    switch (activity?.type) {
        case 'asx-announcement':
            return 'Australian Securities Exchange';
        case 'media':
            return activity.source.name;
        case 'reddit':
        case 'redditComment':
            return `/u/${activity?.author?.userId}`;
        case 'tweet':
            return `@${activity?.author?.userId}`;
        default:
            return activity?.author?.name ?? undefined;
    }
}
