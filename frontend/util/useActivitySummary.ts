import { Activity } from '@quarterback/types';
import { useMemo } from 'react';
import { author as getAuthor } from './author';

export function engagement(activity: Activity | undefined) {
    if (!activity) return undefined;

    if ('likes' in activity) {
        return activity.likes;
    }

    if ('score' in activity) {
        return activity.score;
    }

    return undefined;
}

export default function useActivitySummary(activity: Activity | undefined) {
    const description = useMemo(() => {
        if (!activity) return undefined;
        if (activity?.type === 'asx-announcement') return undefined;
        if ('summary' in activity) return activity.summary;
        if ('description' in activity) return activity.description;
        if ('body' in activity) return activity.body;

        return undefined;
    }, [activity]);

    const url = useMemo(() => {
        if (!activity) return undefined;
        if (activity?.type === 'asx-announcement') return activity.pdf;
        if ('url' in activity) return activity.url;
        return undefined;
    }, [activity]);

    const image = useMemo(() => {
        if (!activity) return undefined;
        if (['hotcopper', 'asx-announcement'].includes(activity.type)) return undefined;
        if ('image' in activity) return activity.image;

        return undefined;
    }, [activity]);

    const title = useMemo(() => {
        if (!activity) return undefined;
        if ('title' in activity) return activity.title;
    }, [activity]);

    const author = useMemo(() => {
        if (!activity) return undefined;
        return getAuthor(activity);
    }, [activity]);

    const activityEngagement = useMemo(() => {
        return engagement(activity);
    }, [activity]);

    return { author, title, image, url, description, engagement: activityEngagement };
}
