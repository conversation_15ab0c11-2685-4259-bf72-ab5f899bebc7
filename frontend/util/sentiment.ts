import { Activity, Sentiment } from '@quarterback/types';
import { useMemo } from 'react';
import { isDefined } from '@quarterback/util';
import hasField from '@/util/hasField';

function scaledMagnitude(magnitude: number): number {
    return 4 / (1 + Math.exp(-0.3 * magnitude)) - 2;
}

export function sentimentScore({ score, magnitude }: Sentiment): number {
    return Math.max(Math.min(score * scaledMagnitude(magnitude), 1), -1);
}

export enum DiscreteSentiment {
    NEGATIVE,
    LACKING,
    NEUTRAL,
    POSITIVE
}

export const SENTIMENT_COLORS: Record<DiscreteSentiment, string> = {
    [DiscreteSentiment.NEGATIVE]: '#f87171',
    [DiscreteSentiment.LACKING]: '#facc15',
    [DiscreteSentiment.NEUTRAL]: '#e2e8f0',
    [DiscreteSentiment.POSITIVE]: '#4ade80'
};

export function discreteSentiment(score: number): DiscreteSentiment {
    if (score < -0.2) return DiscreteSentiment.NEGATIVE;
    if (score < -0.1) return DiscreteSentiment.LACKING;
    if (score < 0.1) return DiscreteSentiment.NEUTRAL;
    else return DiscreteSentiment.POSITIVE;
}

export function useAverageSentiment(activities: Array<Activity>): number {
    return useMemo(() => {
        const agg = [...activities].filter(hasField('sentiment')).reduce(
            (agg, activity) => ({
                count: agg.count + 1,
                sum: agg.sum + sentimentScore(activity.sentiment)
            }),
            { count: 0, sum: 0 }
        );

        return agg.count > 0 ? agg.sum / agg.count : 0;
    }, [activities]);
}

export function useSentimentCount(activities: Array<Activity>): [number, number, number, number] {
    return useMemo(() => {
        const sentiment = activities
            .map((it) => it.sentiment)
            .filter(isDefined)
            .map((sentiment) => sentimentScore(sentiment))
            .map((score) => discreteSentiment(score));

        return [
            sentiment.filter((it) => it === DiscreteSentiment.NEGATIVE).length,
            sentiment.filter((it) => it === DiscreteSentiment.LACKING).length,
            sentiment.filter((it) => it === DiscreteSentiment.NEUTRAL).length,
            sentiment.filter((it) => it === DiscreteSentiment.POSITIVE).length
        ];
    }, [activities]);
}

export function useSentimentBands(activities: Array<Activity>): [number, number, number, number] {
    return useMemo(() => {
        const sentiment = activities
            .map((it) => it.sentiment)
            .filter(isDefined)
            .map((sentiment) => sentimentScore(sentiment))
            .map((score) => discreteSentiment(score));

        if (sentiment.length) {
            return [
                sentiment.filter((it) => it === DiscreteSentiment.NEGATIVE).length /
                sentiment.length,
                sentiment.filter((it) => it === DiscreteSentiment.LACKING).length /
                sentiment.length,
                sentiment.filter((it) => it === DiscreteSentiment.NEUTRAL).length /
                sentiment.length,
                sentiment.filter((it) => it === DiscreteSentiment.POSITIVE).length /
                sentiment.length
            ];
        } else {
            return [0, 0, 1, 0];
        }
    }, [activities]);
}
