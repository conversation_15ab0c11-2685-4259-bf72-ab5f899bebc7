import { Activity2, ActivityFormData, ActivitySource, Format } from '@quarterback/types';
import { parseISO } from 'date-fns';

/**
 * Converts form data to Activity2 format for API submission
 */
export function mapFormDataToActivity2(
    formData: ActivityFormData,
    { symbol, exchange }: { symbol?: string; exchange?: string }
): Activity2 {
    let postedDate: Date | undefined;
    if (formData.posted) {
        postedDate = parseISO(formData.posted);
    }

    const activity: Partial<Activity2> = {
        title: formData.title,
        body: formData.body,
        url: formData.url,
        image:
            formData?.image && formData?.image?.length > 0 ? formData.image : undefined,
        posted: postedDate,
        likes: formData?.likes ? parseInt(formData.likes, 10) : undefined,
        symbol,
        exchange
    };

    if (formData.files) {
        activity.files = formData.files;
    }

    // Add author if provided
    if (formData.author) {
        activity.author = formData.author;
    }

    // Set isBroadcast based on format
    activity.format = formData.format as Format;

    // Add source-specific fields
    switch (formData.source) {
        case 'tweet':
            activity.tweet = {};

            if ((formData as any).views) {
                activity.views = parseInt((formData as any).views, 10) || 0;
            }
            break;

        case 'hotcopper':
            const hcData = formData as any;
            activity.hotcopper = {
                sentiment: hcData.sentiment,
                disclosure: hcData.disclosure
            };
            if (hcData.thread) {
                activity.hotcopper.thread = {
                    id: hcData.thread,
                    views: hcData?.threadViews ?? 0
                };
            }
            break;

        case 'linkedIn':
            const liData = formData as any;
            activity.linkedIn = {};
            if (liData.comments) activity.comments = parseInt(liData.comments, 10) || 0;
            break;

        case 'reddit':
            const redditData = formData as any;
            activity.redditPost = {
                subreddit: redditData.subreddit
            };
            if (redditData.score)
                activity.redditPost.score = parseInt(redditData.score, 10) || 0;
            if (redditData.ratio)
                activity.redditPost.ratio = parseFloat(redditData.ratio) || 0;
            break;

        case 'media':
            activity.media = {
                image:
                    formData?.image && formData?.image?.length > 0
                        ? formData.image
                        : undefined,
                source: {
                    name: (formData as any).newsSource.name,
                    domain: (formData as any).newsSource.url
                }
            };
            break;

        case 'asx-announcement':
            const asxData = formData as any;
            activity.asx = {
                sensitive: asxData.sensitive || false
            };
            break;
        case 'advfn':
            activity.advfn = {};
            break;
        case 'applePodcast':
            activity.applePodcast = {};
            break;
        case 'audible':
            activity.audible = {};
            break;
        case 'aussiestockforums':
            activity.aussiestockforums = {};
            break;
        case 'castbox':
            activity.castbox = {};
            break;
        case 'clubhouse':
            activity.clubhouse = {};
            break;
        case 'iheartradio':
            activity.iheartradio = {};
            break;
        case 'investorhub':
            activity.investorhub = {};
            break;
        case 'medium':
            activity.medium = {};
            break;
        case 'pinterest':
            activity.pinterest = {};
            break;
        case 'quora':
            activity.quora = {};
            break;
        case 'slack':
            activity.slack = {};
            break;
        case 'snapchat':
            activity.snapchat = {};
            break;
        case 'spotify':
            activity.spotify = {};
            break;
        case 'stocktwits':
            activity.stocktwits = {};
            break;
        case 'strawman':
            activity.strawman = {};
            break;
        case 'tradingqna':
            activity.tradingqna = {};
            break;
        case 'tumblr':
            activity.tumblr = {};
            break;
        case 'vimeo':
            activity.vimeo = {};
            break;
        case 'wechat':
            activity.wechat = {};
            break;
        case 'whatsapp':
            activity.whatsapp = {};
            break;
        case 'whirlpoolfinance':
            activity.whirlpoolfinance = {};
            break;
        case 'youtube':
            activity.youtube = {};
            break;
        case 'bogleheads':
            activity.bogleheads = {};
            break;
        case 'discord':
            activity.discord = {};
            break;
        case 'telegram':
            activity.telegram = {};
            break;
        case 'facebook':
            activity.facebook = {};
            break;
        case 'instagram':
            activity.instagram = {};
            break;
        case 'tiktok':
            activity.tiktok = {};
            break;
        case 'call':
            activity.call = {};
            break;
        case 'meeting':
            activity.meeting = {};
            break;
        case 'presentation':
            activity.presentation = {};
            break;
        case 'event':
            activity.event = {};
            break;
        case 'other':
            activity.other = {};
            break;
    }

    return activity as Activity2;
}

function getSource(activity: Activity2): ActivitySource | undefined {
    if ('asx' in activity) {
        return 'asx-announcement';
    }
    if ('tweet' in activity) {
        return 'tweet';
    }
    if ('media' in activity) {
        return 'media';
    }
    if ('hotcopper' in activity) {
        return 'hotcopper';
    }
    if ('linkedIn' in activity) {
        return 'linkedIn';
    }
    if ('redditPost' in activity) {
        return 'reddit';
    }
    if ('advfn' in activity) {
        return 'advfn';
    }
    if ('applePodcast' in activity) {
        return 'applePodcast';
    }
    if ('audible' in activity) {
        return 'audible';
    }
    if ('aussiestockforums' in activity) {
        return 'aussiestockforums';
    }
    if ('castbox' in activity) {
        return 'castbox';
    }
    if ('clubhouse' in activity) {
        return 'clubhouse';
    }
    if ('iheartradio' in activity) {
        return 'iheartradio';
    }
    if ('investorhub' in activity) {
        return 'investorhub';
    }
    if ('medium' in activity) {
        return 'medium';
    }
    if ('pinterest' in activity) {
        return 'pinterest';
    }
    if ('quora' in activity) {
        return 'quora';
    }
    if ('slack' in activity) {
        return 'slack';
    }
    if ('snapchat' in activity) {
        return 'snapchat';
    }
    if ('spotify' in activity) {
        return 'spotify';
    }
    if ('stocktwits' in activity) {
        return 'stocktwits';
    }
    if ('strawman' in activity) {
        return 'strawman';
    }
    if ('tradingqna' in activity) {
        return 'tradingqna';
    }
    if ('tumblr' in activity) {
        return 'tumblr';
    }
    if ('vimeo' in activity) {
        return 'vimeo';
    }
    if ('wechat' in activity) {
        return 'wechat';
    }
    if ('whatsapp' in activity) {
        return 'whatsapp';
    }
    if ('whirlpoolfinance' in activity) {
        return 'whirlpoolfinance';
    }
    if ('youtube' in activity) {
        return 'youtube';
    }
    if ('bogleheads' in activity) {
        return 'bogleheads';
    }
    if ('discord' in activity) {
        return 'discord';
    }
    if ('telegram' in activity) {
        return 'telegram';
    }
    if ('facebook' in activity) {
        return 'facebook';
    }
    if ('instagram' in activity) {
        return 'instagram';
    }
    if ('tiktok' in activity) {
        return 'tiktok';
    }
    if ('call' in activity) {
        return 'call';
    }
    if ('meeting' in activity) {
        return 'meeting';
    }
    if ('presentation' in activity) {
        return 'presentation';
    }
    if ('event' in activity) {
        return 'event';
    }
    if ('other' in activity) {
        return 'other';
    }
    return undefined;
}

/**
 * Converts Activity2 to form data format for editing
 */
export function mapActivity2ToFormData(activity: Activity2): ActivityFormData {
    // TODO : WIP...to be completed when implementing feature for editiong actiivity

    const source = getSource(activity);
    const format = activity.format || 'chatter';

    if (!source && !format) {
        return {} as ActivityFormData;
    }

    switch (format) {
        case 'media':
            return {
                source: 'media',
                format,
                newsSource: activity.media?.source || {},
                body: activity.body || '',
                title: activity.title || '',
                url: activity.url || '',
                image: activity.image || '',
                posted: activity.posted ? activity.posted.toISOString() : '',
                likes: activity.likes ? activity.likes.toString() : '0'
            } as ActivityFormData;

        case 'call':
            return {
                source: 'call',
                format,
                body: activity.body || '',
                url: activity.url || '',
                image: activity.image || '',
                posted: activity.posted ? activity.posted.toISOString() : '',
                likes: activity.likes ? activity.likes.toString() : '0'
            } as ActivityFormData;

        case 'meeting':
            return {
                source: 'meeting',
                format,
                body: activity.body || '',
                url: activity.url || '',
                image: activity.image || '',
                posted: activity.posted ? activity.posted.toISOString() : '',
                likes: activity.likes ? activity.likes.toString() : '0'
            } as ActivityFormData;

        case 'presentation':
            return {
                source: 'presentation',
                format
            } as ActivityFormData;

        case 'event':
            return {
                source: 'event',
                format
            } as ActivityFormData;

        case 'other':
            return {
                source: 'other',
                format
            } as ActivityFormData;
        default:
            break;
    }

    return {} as ActivityFormData;
}
