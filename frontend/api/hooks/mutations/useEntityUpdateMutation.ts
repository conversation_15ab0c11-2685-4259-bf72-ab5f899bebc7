import { usePutMutation } from '@/api/hooks/util/useMutation';
import { ListedEntity } from '@quarterback/types';
import { useCallback, useMemo } from 'react';
import { useSWRConfig } from 'swr';

interface UpdateEntityParams extends ListedEntity {
    organisation: string;
}

export default function useEntityUpdateMutation() {
    const { mutate, cache } = useSWRConfig();

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/entities.*$/,
                    /^\$inf\$\/v1\/entities.*$/,
                    /^\/admin\/entities.*$/,
                    /^\$inf\$\/admin\/entities.*$/,
                    /^\/v1\/organisations.*$/,
                    /^\$inf\$\/v1\/organisations.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    const mutation = usePutMutation<ListedEntity, UpdateEntityParams>('/v1/entities');

    return useMemo(
        () => ({
            async updateEntity(params: UpdateEntityParams) {
                const result = await mutation.trigger(params);
                await invalidate();
                return result;
            },
            isLoading: mutation.isMutating,
            error: mutation.error
        }),
        [invalidate, mutation]
    );
}
