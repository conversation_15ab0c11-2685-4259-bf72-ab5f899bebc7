import { usePostMutation } from '@/api/hooks/util/useMutation';
import z from 'zod';
import { ListedEntity, Organisation } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

const InsightMutation = z.object({
    organisation: Organisation,
    entity: ListedEntity
});

const InsightParams = z.object({
    organisation: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    from: z.date(),
    to: z.date(),
    ids: z.string().array().optional(),
    limit: z.number().optional(),
    type: z.string().optional().default('mixed'),
    extended: z.boolean().optional()
});

type InsightMutation = z.infer<typeof InsightMutation>;
type InsightParams = z.infer<typeof InsightParams>;

export default function useInsightsMutation(mutationArgs: InsightMutation) {
    const { entity, organisation } = mutationArgs;
    const { mutate, cache } = useSWRConfig();

    const insight = usePostMutation<string, InsightParams>(
        organisation && entity ? `/v1/insights` : null
    );

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/organisations\/.*\/activities.*$/,
                    /^\$inf\$\/v1\/organisations\/.*\/activities.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async getInsight(...args: Parameters<typeof insight.trigger>) {
                const response: { data: string | undefined; error: any } = {
                    data: undefined,
                    error: undefined
                };
                try {
                    await invalidate();
                    response.data = await insight.trigger(...args);
                } catch (error) {
                    response.error = error;
                } finally {
                    return response;
                }
            }
        }),
        [insight, invalidate]
    );
}
