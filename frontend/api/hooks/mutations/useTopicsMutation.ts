import { usePostMutation } from '@/api/hooks/util/useMutation';
import z from 'zod';
import { ListedEntity, Organisation, Topic } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

const TopicsMutation = z.object({
    organisation: Organisation,
    entity: ListedEntity
});

const TopicstParams = z.object({
    organisation: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    from: z.date(),
    to: z.date(),
    ids: z.string().array().optional()
});

type TopicsMutation = z.infer<typeof TopicsMutation>;
type TopicsParams = z.infer<typeof TopicstParams>;

export default function useTopicsMutation(mutationArgs: TopicsMutation) {
    const { entity, organisation } = mutationArgs;
    const { mutate, cache } = useSWRConfig();

    const topics = usePostMutation<Array<Topic>, TopicsParams>(
        organisation && entity ? `/v1/insights/topics` : null
    );

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/organisations\/.*\/activities.*$/,
                    /^\$inf\$\/v1\/organisations\/.*\/activities.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async getTopics(...args: Parameters<typeof topics.trigger>) {
                const response: { data: Array<Topic> | undefined; error: any } = {
                    data: undefined,
                    error: undefined
                };
                try {
                    await invalidate();
                    response.data = await topics.trigger(...args);
                } catch (error) {
                    response.error = error;
                } finally {
                    return response;
                }
            }
        }),
        [invalidate, topics]
    );
}
