import { Activity, ListedEntity, Organisation, WordCloudItem } from '@quarterback/types';
import z from 'zod';

import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { add } from 'date-fns';
import { useMemo } from 'react';

const WordCloudFilters = z.object({
    chatter: z.boolean().optional()
});

type WordCloudFilters = z.infer<typeof WordCloudFilters>;

export default function useWordCloud(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    from: Date,
    to: Date,
    filters: WordCloudFilters = {}
) {
    const params = new URLSearchParams({
        organisation: organisation?.id || '',
        from: from.toISOString(),
        to: add(to, { days: 1 }).toISOString(),
        ...(entity && {
            symbol: entity.symbol,
            exchange: entity.exchange
        }),
        ...(filters.chatter ? { chatter: 'true' } : {})
    });

    const { data, error, isLoading } = useAPI<Array<Activity>>(
        api,
        entity && organisation ? `/v1/insights/wordcloud` : null,
        params
    );

    return useMemo(
        () => ({
            data: data ? z.array(WordCloudItem).parse(data) : undefined,
            isLoading,
            error
        }),
        [data, isLoading, error]
    );
}
