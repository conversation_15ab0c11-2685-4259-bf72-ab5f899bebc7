import { useCallback, useEffect, useMemo, useState } from 'react';
import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { Author } from '@quarterback/types';

interface PaginatedResponse {
    authors: Author[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
    };
}

interface UsePaginatedAuthorsOptions {
    initialLimit?: number;
    prefetch?: boolean;
    sourceFilter?: string;
}

export default function usePaginatedAuthors(options: UsePaginatedAuthorsOptions = {}) {
    const { initialLimit = 10, prefetch = true, sourceFilter } = options;

    const [searchTerm, setSearchTerm] = useState('');
    const [limit] = useState(initialLimit);
    const [offset, setOffset] = useState(0);
    const [allAuthors, setAllAuthors] = useState<Author[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [isSearching, setIsSearching] = useState(false);

    const filter = useMemo(() => {
        if (!sourceFilter) return undefined;

        return ['call', 'meeting', 'presentation', 'event', 'other'].includes(
            sourceFilter
        )
            ? 'other'
            : sourceFilter;
    }, [sourceFilter]);

    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());

    if (searchTerm) {
        params.append('search', searchTerm);
    }

    if (filter) {
        params.append('source', filter);
    }

    const { data, error, isLoading, mutate } = useAPI<PaginatedResponse>(
        api,
        '/v1/authors',
        params,
        {
            revalidateOnFocus: false,
            revalidateIfStale: false,
            dedupingInterval: 10000,
            revalidateOnMount: prefetch
        }
    );

    useEffect(() => {
        if (data) {
            try {
                const authors = data.authors || [];
                if (offset === 0 || isSearching) {
                    setAllAuthors(authors);
                } else {
                    setAllAuthors((prev) => [...prev, ...authors]);
                }

                const total = data.pagination?.total ?? authors.length;
                setTotalCount(total);

                setIsSearching(false);
            } catch (err) {
                console.error('Error processing authors data:', err);

                if (offset === 0 || isSearching) {
                    setAllAuthors([]);
                }
                setTotalCount(0);
                setIsSearching(false);
            }
        }
    }, [data, offset, isSearching]);

    useEffect(() => {
        if (prefetch) {
            mutate();
        }
    }, [prefetch, mutate]);

    const handleSearch = useCallback((term: string) => {
        setSearchTerm(term);
        setOffset(0);
        setIsSearching(true);
    }, []);

    const loadMore = useCallback(() => {
        if (!isLoading && allAuthors.length < totalCount) {
            setOffset((prev) => prev + limit);
        }
    }, [isLoading, allAuthors.length, totalCount, limit]);

    const hasMore = useMemo(() => {
        return allAuthors.length < totalCount && !isLoading;
    }, [allAuthors.length, totalCount, isLoading]);

    return {
        authors: allAuthors,
        isLoading,
        error,
        totalCount,
        hasMore,
        searchTerm,
        handleSearch,
        loadMore,
        refresh: mutate
    };
}
