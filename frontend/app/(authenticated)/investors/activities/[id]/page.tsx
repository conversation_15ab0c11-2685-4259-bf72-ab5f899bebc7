'use client';

import { Bars4Icon, ChatBubbleLeftEllipsisIcon } from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import DetailTab from './DetailTab';
import CommentTab from './CommentTab';
import Button from '@/components/ui/Button';

const TABS = {
    DETAILS: 'details',
    COMMENTS: 'comments'
};

export default function ActivityPage({ params }: { params: { id: string } }) {
    const [activeTab, setActiveTab] = useState(TABS.DETAILS);

    return (
        <div className="flex flex-col  h-full">
            <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
                <div className="flex px-4 text-sm font-medium h-[52px] items-stretch pt-2">
                    <Button
                        variant="tertiary"
                        size="sm"
                        active={activeTab === TABS.DETAILS}
                        onClick={() => setActiveTab(TABS.DETAILS)}>
                        <div className="flex items-center space-x-1">
                            <Bars4Icon className="size-4 mx-1" />
                            Details
                        </div>
                    </Button>

                    {/* keep this do not remove -- will uncomment soon */}

                    {/* <Button
                        variant="tertiary"
                        size="sm"
                        active={activeTab === TABS.COMMENTS}
                        onClick={() => setActiveTab(TABS.COMMENTS)}>
                        <div className="flex items-center space-x-1">
                            <ChatBubbleLeftEllipsisIcon className="size-4 mx-1" />
                            Comments
                        </div>
                    </Button> */}
                </div>
            </div>

            {activeTab === TABS.DETAILS ? (
                <DetailTab params={params} />
            ) : (
                <CommentTab params={params} />
            )}

            {/* Sticky Footer */}
            {/* {activeTab === TABS.DETAILS && <div className="bg-white border-t border-gray-200 sticky bottom-0 px-4 py-3 text-sm text-gray-700 flex justify-between items-center">
                <span>Footer</span>

            </div>} */}
        </div>
    );
}
