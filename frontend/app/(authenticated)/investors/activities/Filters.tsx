import {
    Field,
    Label,
    Popover,
    PopoverButton,
    PopoverPanel,
    Switch,
    Transition
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/20/solid';
import { Bars3BottomLeftIcon } from '@heroicons/react/24/outline';
import { useMemo } from 'react';
import FilterMenu from './FilterMenu';

export default function Filters({
    sources,
    formats,
    sourceFilters,
    setSourceFilters,
    formatFilters,
    handleFormatFilters,
    hasAnnouncement,
    showArchived,
    showFlagged,
    showPriceSensitive,
    handleHasAnouncementFilter,
    setShowArchived,
    setShowFlagged,
    setShowPriceSensitive
}: {
    sources: Array<string>;
    formats: Array<string>;
    sourceFilters: Array<string>;
    setSourceFilters: (filters: Array<string>) => void;
    formatFilters: Array<string>;
    handleFormatFilters: (filters: Array<string>) => void;
    hasAnnouncement: boolean;
    showArchived: boolean;
    showFlagged: boolean;
    showPriceSensitive: boolean;
    handleHasAnouncementFilter: (hasAnnouncement: boolean) => void;
    setShowArchived: (showArchived: boolean) => void;
    setShowFlagged: (showFlagged: boolean) => void;
    setShowPriceSensitive: (priceSensitive: boolean) => void;
}) {
    const count = useMemo(() => {
        let count = 0;

        if (sourceFilters.length) count++;
        if (formatFilters.length) count++;
        if (hasAnnouncement) count++;

        return count;
    }, [sourceFilters, formatFilters, hasAnnouncement]);

    function reset() {
        setSourceFilters([]);
        handleFormatFilters([]);
        handleHasAnouncementFilter(false);
        setShowArchived(false);
        setShowFlagged(false);
        setShowPriceSensitive(false);
    }

    const anouncementOnlyFilter = useMemo(() => {
        return formatFilters.length === 1 && formatFilters[0] === 'Announcement';
    }, [formatFilters]);

    const includesAnnouncementFilter = useMemo(() => {
        return formatFilters.includes('Announcement');
    }, [formatFilters]);

    return (
        <Popover className="relative">
            <PopoverButton>
                <div className="flex items-center gap-x-1 hover:bg-gray-100 p-2 rounded-md cursor-pointer -mx-2">
                    <Bars3BottomLeftIcon className="size-4" />
                    <span className="text-sm text-gray-500">
                        Filters{count ? ` (${count})` : ''}
                    </span>
                </div>
            </PopoverButton>
            <Transition
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95">
                <PopoverPanel className="absolute min-w-72 flex flex-col bg-white z-50 rounded-md py-2  shadow-lg ring-1 ring-gray-900/5 gap-y-2 focus:outline-none">
                    <FilterMenu
                        menuName="Format"
                        items={formats}
                        selectedItems={formatFilters}
                        onChange={handleFormatFilters}
                    />
                    {!anouncementOnlyFilter ? (
                        <FilterMenu
                            menuName="Sources"
                            items={sources}
                            selectedItems={sourceFilters}
                            onChange={setSourceFilters}
                        />
                    ) : null}

                    <Field className="flex items-center justify-between px-2">
                        <span className="flex flex-grow flex-col">
                            <Label
                                as="span"
                                passive
                                className="text-sm font-medium leading-6 text-gray-900">
                                Announcement days only
                            </Label>
                        </span>
                        <Switch
                            checked={hasAnnouncement}
                            onChange={handleHasAnouncementFilter}
                            className="group relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 data-[checked]:bg-indigo-600">
                            <span
                                aria-hidden="true"
                                className="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out group-data-[checked]:translate-x-5"
                            />
                        </Switch>
                    </Field>
                    {includesAnnouncementFilter || hasAnnouncement ? (
                        <Field className="flex items-center justify-between mt-2 px-2">
                            <span className="flex flex-grow flex-col">
                                <Label
                                    as="span"
                                    passive
                                    className="text-sm font-medium leading-6 text-gray-900">
                                    Show price sensitive only
                                </Label>
                            </span>
                            <Switch
                                checked={showPriceSensitive}
                                onChange={setShowPriceSensitive}
                                className="group relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 data-[checked]:bg-indigo-600">
                                <span
                                    aria-hidden="true"
                                    className="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out group-data-[checked]:translate-x-5"
                                />
                            </Switch>
                        </Field>
                    ) : null}
                    <Field className="flex items-center justify-between mt-2 px-2">
                        <span className="flex flex-grow flex-col">
                            <Label
                                as="span"
                                passive
                                className="text-sm font-medium leading-6 text-gray-900">
                                Show archived activities
                            </Label>
                        </span>
                        <Switch
                            checked={showArchived}
                            onChange={setShowArchived}
                            className="group relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 data-[checked]:bg-indigo-600">
                            <span
                                aria-hidden="true"
                                className="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out group-data-[checked]:translate-x-5"
                            />
                        </Switch>
                    </Field>

                    <Field className="flex items-center justify-between mt-2 px-2">
                        <span className="flex flex-grow flex-col">
                            <Label
                                as="span"
                                passive
                                className="text-sm font-medium leading-6 text-gray-900">
                                Show flagged activities
                            </Label>
                        </span>
                        <Switch
                            checked={showFlagged}
                            onChange={setShowFlagged}
                            className="group relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 data-[checked]:bg-indigo-600">
                            <span
                                aria-hidden="true"
                                className="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out group-data-[checked]:translate-x-5"
                            />
                        </Switch>
                    </Field>

                    <button
                        type="button"
                        className="inline-flex items-center justify-center gap-x-1 rounded-md bg-indigo-50 px-2 py-1 text-sm font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100"
                        onClick={reset}>
                        <XMarkIcon aria-hidden="true" className="-ml-0.5 h-5 w-5" />
                        Reset all
                    </button>
                </PopoverPanel>
            </Transition>
        </Popover>
    );
}
