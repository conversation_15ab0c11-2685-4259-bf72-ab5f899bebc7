'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { Activity2 } from '@quarterback/types';

interface ActivityFormContextType {
    isFormOpen: boolean;
    openForm: () => void;
    closeForm: () => void;
    onSubmit?: (data: Activity2[]) => Promise<any>;
}

const ActivityFormContext = createContext<ActivityFormContextType | undefined>(undefined);

interface ActivityFormProviderProps {
    children: React.ReactNode;
    onSubmit?: (data: Activity2[]) => Promise<any>;
}

export function ActivityFormProvider({ children, onSubmit }: ActivityFormProviderProps) {
    const [isFormOpen, setIsFormOpen] = useState(false);

    const openForm = useCallback(() => {
        setIsFormOpen(true);
    }, []);

    const closeForm = useCallback(() => {
        setIsFormOpen(false);
    }, []);

    const value: ActivityFormContextType = {
        isFormOpen,
        openForm,
        closeForm,
        onSubmit
    };

    return (
        <ActivityFormContext.Provider value={value}>
            {children}
        </ActivityFormContext.Provider>
    );
}

export function useActivityForm() {
    const context = useContext(ActivityFormContext);
    if (context === undefined) {
        throw new Error('useActivityForm must be used within an ActivityFormProvider');
    }
    return context;
}
