import { SearchableSelectField } from '@/components/forms/fields/SearchableSelectField';
import usePaginatedSocialSources from '@/api/hooks/usePaginatedSocialSources';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SocialSource } from '@/api/hooks/useSocialSources';
import { FormAction } from '../reducers/activityFormReducer';
import NewSourceModal from './NewsourceModel';
import { ActivitySource } from '@quarterback/types';

interface SocialSourceDropdownProps {
    value: string;
    dispatch: React.Dispatch<FormAction>;
    error?: string;
    addNewAuthor?: boolean;
}

export default function SocialSourceDropdown({
    value,
    dispatch,
    error,
    addNewAuthor = false
}: SocialSourceDropdownProps) {
    const {
        sources: socialSources,
        handleSearch,
        loadMore,
        hasMore,
        isLoading: isLoadingSources
    } = usePaginatedSocialSources({
        initialLimit: 20,
        prefetch: true
    });

    const [newSource, setNewSource] = useState(false);

    const sourceOptions = useMemo(() => {
        const sources = socialSources.map((source) => ({
            key: source.sourceKey,
            value: source.sourceKey,
            label: source.name
        }));

        if (addNewAuthor) {
            return sources.concat({
                key: 'other',
                value: 'other',
                label: 'Other'
            });
        }

        return sources;
    }, [socialSources, addNewAuthor]);

    const handleSocialSourceChange = useCallback(
        (sourceData: SocialSource | undefined) => {
            if (!sourceData) return;

            dispatch({
                type: 'SET_SOURCE',
                payload: {
                    sourceType: sourceData.sourceKey as ActivitySource
                }
            });
        },
        [dispatch]
    );

    const handleSourceChange = useCallback(
        (value: string) => {
            let selectedSource: SocialSource | undefined;
            if (value) {
                if (value === 'other') {
                    selectedSource = {
                        sourceKey: 'other',
                        name: 'Other',
                        url: '',
                        logo: null
                    } as SocialSource;
                } else {
                    selectedSource = socialSources.find(
                        (source) => source.sourceKey === value
                    );
                }

                if (selectedSource) {
                    handleSocialSourceChange(selectedSource);
                } else if (value.trim() !== '') {
                    if (handleSearch) {
                        handleSearch(value);
                    }
                }
            } else {
                handleSocialSourceChange(undefined);
                handleSearch('');
            }
        },
        [socialSources, handleSearch, handleSocialSourceChange]
    );

    const handleNewSource = useCallback(() => {
        setNewSource(true);
    }, []);

    useEffect(() => {
        if (value && !socialSources.some((source) => source.sourceKey === value)) {
            const selectedSource = socialSources.find(
                (source) => source.sourceKey === value
            );
            if (selectedSource) {
                handleSocialSourceChange(selectedSource);
            }
        }
    }, [value, socialSources, handleSocialSourceChange]);

    return (
        <>
            <SearchableSelectField
                label="Source"
                value={value}
                onChange={handleSourceChange}
                options={sourceOptions}
                error={error}
                onSearch={handleSearch}
                onLoadMore={loadMore}
                hasMore={hasMore}
                isLoading={isLoadingSources}
                searchPlaceholder="Search sources..."
                actionButton={
                    !addNewAuthor
                        ? {
                              label: "Can't find source",
                              onClick: handleNewSource
                          }
                        : undefined
                }
            />
            <NewSourceModal open={newSource} setOpen={setNewSource} />
        </>
    );
}
