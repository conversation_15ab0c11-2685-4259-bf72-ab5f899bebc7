import { engagement } from '@/util/useActivitySummary';
import TopEngagedPostRow from './TopEngagedPostRow';
import { Activity } from '@quarterback/types';
import React, { useMemo } from 'react';

export default function TopEngagedPosts({ activities }: { activities: Array<Activity> }) {
    const topPosts = useMemo(() => {
        return activities
            .filter(
                (activity) =>
                    engagement(activity) !== undefined && engagement(activity)! > 0
            )
            .sort((a, b) => {
                return (engagement(b) ?? 0) - (engagement(a) ?? 0);
            })
            .slice(0, 5);
    }, [activities]);

    if (topPosts.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500">
                <p className="text-sm">No engaged posts found</p>
            </div>
        );
    }

    return (
        <div className="overflow-hidden">
            <div className="overflow-x-auto">
                <table className="min-w-full">
                    <thead>
                        <tr className="border-b border-gray-200 bg-gray-100">
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Author
                            </th>
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-4">
                                Body
                            </th>
                            <th className="text-right text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Engagement
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {topPosts.map((activity) => (
                            <TopEngagedPostRow key={activity.id} activity={activity} />
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}
