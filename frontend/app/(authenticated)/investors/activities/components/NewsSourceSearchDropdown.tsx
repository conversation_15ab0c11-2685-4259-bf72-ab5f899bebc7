import { SearchableSelectField } from '@/components/forms/fields/SearchableSelectField';
import usePaginatedNewsSources from '@/api/hooks/usePaginatedNewsSources';
import { useCallback, useEffect, useMemo } from 'react';
import { NewsSource } from '@/api/hooks/useNewsSources';
import { FormAction } from '../reducers/activityFormReducer';

interface NewsSourceSearchDropdownProps {
    value: string;
    dispatch: React.Dispatch<FormAction>;
    error?: string;
}

export default function NewsSourceSearchDropdown({
    value,
    dispatch,
    error
}: NewsSourceSearchDropdownProps) {
    const {
        sources: newsSources,
        handleSearch,
        loadMore,
        hasMore,
        isLoading: isLoadingSources
    } = usePaginatedNewsSources({
        initialLimit: 20,
        prefetch: true
    });

    // Source options - memoized to prevent unnecessary recalculations
    const sourceOptions = useMemo(() => {
        return newsSources.map((source) => ({
            key: `news-source-${source.url}`,
            value: source.url,
            label: source.name
        }));
    }, [newsSources]);

    // Handle news source change - this replaces the external handleNewsSourceChange
    const handleNewsSourceChange = useCallback(
        (sourceData: NewsSource | undefined) => {
            dispatch({
                type: 'SET_SOURCE',
                payload: {
                    sourceType: 'media',
                    sourceData
                }
            });
        },
        [dispatch]
    );

    const handleSourceChange = useCallback(
        (value: string) => {
            if (value) {
                const selectedSource = newsSources.find((source) => source.url === value);

                if (selectedSource) {
                    handleNewsSourceChange(selectedSource);
                } else {
                    if (handleSearch) {
                        handleSearch(value);
                    }
                }
            } else {
                handleNewsSourceChange(undefined);
                handleSearch('');
            }
        },
        [newsSources, handleSearch, handleNewsSourceChange]
    );

    useEffect(() => {
        if (value && !newsSources.some((source) => source.url === value)) {
            handleSearch(value);
        } else if (value) {
            const selectedSource = newsSources.find((source) => source.url === value);
            if (selectedSource) {
                handleNewsSourceChange(selectedSource);
            }
        }
    }, [value, newsSources, handleSearch, handleNewsSourceChange]);

    return (
        <SearchableSelectField
            label="Source"
            value={value}
            onChange={handleSourceChange}
            options={sourceOptions}
            error={error}
            onSearch={handleSearch}
            onLoadMore={loadMore}
            hasMore={hasMore}
            isLoading={isLoadingSources}
            searchPlaceholder="Search sources..."
        />
    );
}
