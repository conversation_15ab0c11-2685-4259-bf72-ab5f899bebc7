import { TextField } from '@/components/forms/fields/TextField';
import { useFormValidation } from '@/hooks/useFormValidation';

import { useCallback, useEffect, useState } from 'react';
import useAuthorMutation from '@/api/hooks/mutations/useAuthorMutation';
import { Author, AuthorFormData } from '@quarterback/types';
import { FormAction } from '../reducers/activityFormReducer';
import SocialSourceDropdown from './SocialSourceDropdown';

interface AuthorCreationFormProps {
    onSuccess: (author: Author) => void;
    onCancel: (e: React.MouseEvent) => void;
    sourceFilter?: string;
}

function ProfileImage({ src }: { src: string | undefined }) {
    const [hasError, setHasError] = useState(false);

    useEffect(() => {
        setHasError(false);
    }, [src]);

    return (
        <div className="w-16 h-16 rounded-full bg-gray-200 overflow-hidden">
            {!hasError && src && (
                <img
                    src={src}
                    alt="Profile"
                    className="w-full h-full object-cover"
                    onError={() => setHasError(true)}
                />
            )}
        </div>
    );
}

export default function AuthorCreationForm({
    onSuccess,
    onCancel
}: AuthorCreationFormProps) {
    const [formData, setFormData] = useState<AuthorFormData>({
        userId: '',
        name: '',
        source: 'advfn',
        url: '',
        image: '',
        followers: null,
        following: null
    });

    const { validate, errors, setErrors } = useFormValidation(AuthorFormData, formData);
    const { createAuthor, isLoading } = useAuthorMutation();

    const handleChange = useCallback((field: keyof AuthorFormData, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value
        }));
    }, []);

    const dispatch = useCallback(
        (action: FormAction) => {
            if (action.type === 'SET_SOURCE' && action.payload.sourceType) {
                handleChange('source', action.payload.sourceType);
            }
        },
        [handleChange]
    );

    const handleSubmit = useCallback(
        async (e: React.FormEvent) => {
            e.preventDefault();

            if (await validate()) {
                try {
                    const author = await createAuthor({
                        userId: formData.userId,
                        name: formData.name,
                        source: formData.source,
                        url: formData.url,
                        image: formData.image?.length ? formData.image : undefined,
                        followers: isNaN(Number(formData.followers))
                            ? undefined
                            : Number(formData.followers),
                        following: isNaN(Number(formData.following))
                            ? undefined
                            : Number(formData.following)
                    });
                    onSuccess(author);
                } catch (error) {
                    console.error('Error creating author:', error);
                    if (error instanceof Error) {
                        setErrors({ form: error.message });
                    } else {
                        setErrors({ form: 'Failed to create author' });
                    }
                }
            }
        },
        [formData, validate, createAuthor, onSuccess, setErrors]
    );

    return (
        <form id="create-author-form" onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-4">
                <SocialSourceDropdown
                    value={formData.source}
                    dispatch={dispatch}
                    error={errors['source']}
                    addNewAuthor={true}
                />

                <TextField
                    label="Username"
                    value={formData.userId}
                    onChange={(value) => handleChange('userId', value)}
                    error={errors['userId']}
                    required
                />

                <TextField
                    label="Full Name"
                    value={formData.name}
                    onChange={(value) => handleChange('name', value)}
                    error={errors['name']}
                    required
                />

                <TextField
                    label="Profile URL"
                    value={formData.url || ''}
                    onChange={(value) => handleChange('url', value)}
                    error={errors['url']}
                />

                <TextField
                    label="Profile Image URL"
                    value={formData.image || ''}
                    onChange={(value) => handleChange('image', value)}
                    error={errors['image']}
                />

                <div className="flex items-center justify-between">
                    <div className="mx-3 flex flex-col justify-center items-center">
                        <div className="text-sm font-medium">Profile Image</div>
                        <ProfileImage src={formData.image} />
                    </div>

                    <TextField
                        label="Followers count"
                        type="number"
                        min={0}
                        value={formData.followers?.toString() || ''}
                        onChange={(value) =>
                            handleChange('followers', value ? parseInt(value) : null)
                        }
                        error={errors['followers']}
                    />
                </div>

                {errors.form && (
                    <div className="text-red-500 text-sm mt-2">{errors.form}</div>
                )}
            </div>

            <div className="mt-6 flex items-center justify-end gap-x-6">
                <button
                    type="button"
                    className="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 disabled:bg-gray-200 disabled:pointer-events-none"
                    onClick={onCancel}
                    disabled={isLoading}>
                    Cancel
                </button>
                <button
                    type="submit"
                    className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:bg-indigo-300"
                    disabled={isLoading}>
                    {isLoading ? 'Creating...' : 'Save and Select'}
                </button>
            </div>
        </form>
    );
}
