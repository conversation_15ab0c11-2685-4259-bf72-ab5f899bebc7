import { useCallback } from 'react';
import {
    ActivityFormData,
    ADVFNFormData,
    ApplePodcastFormData,
    ASXFormData,
    AudibleFormData,
    AussieStockForumsFormData,
    BogleheadsFormData,
    CastboxFormData,
    ClubhouseFormData,
    DiscordFormData,
    FacebookFormData,
    HotCopperFormData,
    IHeartRadioFormData,
    InstagramFormData,
    InvestorHubFormData,
    LinkedInFormData,
    MediumFormData,
    MediaFormData,
    PinterestFormData,
    QuoraFormData,
    RedditFormData,
    SlackFormData,
    SnapchatFormData,
    SpotifyFormData,
    StocktwitsFormData,
    StrawmanFormData,
    TelegramFormData,
    TikTokFormData,
    TradingQnAFormData,
    TumblrFormData,
    TwitterFormData,
    VimeoFormData,
    WeChatFormData,
    WhatsAppFormData,
    WhirlpoolFinanceFormData,
    YouTubeFormData,
    CallFormData,
    MeetingFormData,
    PresentationFormData,
    EventFormData,
    OtherFormData
} from '@quarterback/types';
import TwitterForm from '../forms/TwitterForm';
import HotCopperForm from '../forms/HotCopperForm';
import LinkedInForm from '../forms/LinkedInForm';
import RedditForm from '../forms/RedditForm';
import NewsForm from '../forms/NewsForm';
import ASXForm from '../forms/ASXForm';
import FacebookForm from '../forms/FacebookForm';
import InstagramForm from '../forms/InstagramForm';
import YouTubeForm from '../forms/YouTubeForm';
import TikTokForm from '../forms/TikTokForm';
import DiscordForm from '../forms/DiscordForm';
import TelegramForm from '../forms/TelegramForm';
import MediumForm from '../forms/MediumForm';
import QuoraForm from '../forms/QuoraForm';
import SlackForm from '../forms/SlackForm';
import ADVFNForm from '../forms/ADVFNForm';
import ApplePodcastForm from '../forms/ApplePodcastForm';
import AudibleForm from '../forms/AudibleForm';
import AussieStockForumsForm from '../forms/AussieStockForumsForm';
import CastboxForm from '../forms/CastboxForm';
import ClubhouseForm from '../forms/ClubhouseForm';
import IHeartRadioForm from '../forms/IHeartRadioForm';
import InvestorHubForm from '../forms/InvestorHubForm';
import PinterestForm from '../forms/PinterestForm';
import SnapchatForm from '../forms/SnapchatForm';
import SpotifyForm from '../forms/SpotifyForm';
import StocktwitsForm from '../forms/StocktwitsForm';
import StrawmanForm from '../forms/StrawmanForm';
import TradingQnAForm from '../forms/TradingQnAForm';
import TumblrForm from '../forms/TumblrForm';
import VimeoForm from '../forms/VimeoForm';
import WeChatForm from '../forms/WeChatForm';
import WhatsAppForm from '../forms/WhatsAppForm';
import WhirlpoolFinanceForm from '../forms/WhirlpoolFinanceForm';
import BogleheadsForm from '../forms/BogleheadsForm';
import { FormAction } from '../reducers/activityFormReducer';
import CallForm from '../forms/CallForm';
import MeetingForm from '../forms/MeetingForm';
import PresentationForm from '../forms/PresentationForm';
import EventForm from '../forms/EventForm';
import OtherForm from '../forms/OtherForm';

// Component to render the appropriate form based on source
export default function RenderActivityForm({
    formData,
    dispatch
}: {
    formData: ActivityFormData;
    dispatch: React.Dispatch<FormAction>;
}) {
    const setTwitterFormData = useCallback(
        (data: TwitterFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setHotCopperFormData = useCallback(
        (data: HotCopperFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setLinkedInFormData = useCallback(
        (data: LinkedInFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setRedditFormData = useCallback(
        (data: RedditFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setMediaFormData = useCallback(
        (data: MediaFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setASXFormData = useCallback(
        (data: ASXFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setFacebookFormData = useCallback(
        (data: FacebookFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setInstagramFormData = useCallback(
        (data: InstagramFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setYouTubeFormData = useCallback(
        (data: YouTubeFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setTikTokFormData = useCallback(
        (data: TikTokFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setDiscordFormData = useCallback(
        (data: DiscordFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setTelegramFormData = useCallback(
        (data: TelegramFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setMediumFormData = useCallback(
        (data: MediumFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setQuoraFormData = useCallback(
        (data: QuoraFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setSlackFormData = useCallback(
        (data: SlackFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setADVFNFormData = useCallback(
        (data: ADVFNFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setApplePodcastFormData = useCallback(
        (data: ApplePodcastFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setAudibleFormData = useCallback(
        (data: AudibleFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setAussieStockForumsFormData = useCallback(
        (data: AussieStockForumsFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setCastboxFormData = useCallback(
        (data: CastboxFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setClubhouseFormData = useCallback(
        (data: ClubhouseFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setIHeartRadioFormData = useCallback(
        (data: IHeartRadioFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setInvestorHubFormData = useCallback(
        (data: InvestorHubFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setPinterestFormData = useCallback(
        (data: PinterestFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setSnapchatFormData = useCallback(
        (data: SnapchatFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setSpotifyFormData = useCallback(
        (data: SpotifyFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setStocktwitsFormData = useCallback(
        (data: StocktwitsFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setStrawmanFormData = useCallback(
        (data: StrawmanFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setTradingQnAFormData = useCallback(
        (data: TradingQnAFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setTumblrFormData = useCallback(
        (data: TumblrFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setVimeoFormData = useCallback(
        (data: VimeoFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setWeChatFormData = useCallback(
        (data: WeChatFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setWhatsAppFormData = useCallback(
        (data: WhatsAppFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setWhirlpoolFinanceFormData = useCallback(
        (data: WhirlpoolFinanceFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setBogleheadsFormData = useCallback(
        (data: BogleheadsFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setCallFormData = useCallback(
        (data: CallFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setMeetingFormData = useCallback(
        (data: MeetingFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setPresentationFormData = useCallback(
        (data: PresentationFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setEventFormData = useCallback(
        (data: EventFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    const setOtherFormData = useCallback(
        (data: OtherFormData) => {
            Object.entries(data).forEach(([key, value]) => {
                if (formData[key as keyof ActivityFormData] !== value) {
                    dispatch({
                        type: 'UPDATE_FIELD',
                        payload: { field: key, value }
                    });
                }
            });
        },
        [formData, dispatch]
    );

    if (!formData.source || !formData.format) return null;

    switch (formData.format) {
        case 'announcement':
            return (
                <ASXForm
                    formData={formData as ASXFormData}
                    setFormData={setASXFormData}
                />
            );

        case 'media':
            return (
                <NewsForm
                    formData={formData as MediaFormData}
                    setFormData={setMediaFormData}
                />
            );

        case 'call':
            return (
                <CallForm
                    formData={formData as CallFormData}
                    setFormData={setCallFormData}
                />
            );
        case 'meeting':
            return (
                <MeetingForm
                    formData={formData as MeetingFormData}
                    setFormData={setMeetingFormData}
                />
            );

        case 'presentation':
            return (
                <PresentationForm
                    formData={formData as PresentationFormData}
                    setFormData={setPresentationFormData}
                />
            );

        case 'event':
            return (
                <EventForm
                    formData={formData as EventFormData}
                    setFormData={setEventFormData}
                />
            );

        case 'other':
            return (
                <OtherForm
                    formData={formData as OtherFormData}
                    setFormData={setOtherFormData}
                />
            );

        default:
            break;
    }

    switch (formData.source) {
        case 'tweet':
            return (
                <TwitterForm
                    formData={formData as TwitterFormData}
                    setFormData={setTwitterFormData}
                />
            );
        case 'hotcopper':
            return (
                <HotCopperForm
                    formData={formData as HotCopperFormData}
                    setFormData={setHotCopperFormData}
                />
            );
        case 'linkedIn':
            return (
                <LinkedInForm
                    formData={formData as LinkedInFormData}
                    setFormData={setLinkedInFormData}
                />
            );
        case 'reddit':
            return (
                <RedditForm
                    formData={formData as RedditFormData}
                    setFormData={setRedditFormData}
                />
            );

        case 'asx-announcement':
            return (
                <ASXForm
                    formData={formData as ASXFormData}
                    setFormData={setASXFormData}
                />
            );
        case 'facebook':
            return (
                <FacebookForm
                    formData={formData as FacebookFormData}
                    setFormData={setFacebookFormData}
                />
            );
        case 'instagram':
            return (
                <InstagramForm
                    formData={formData as InstagramFormData}
                    setFormData={setInstagramFormData}
                />
            );
        case 'youtube':
            return (
                <YouTubeForm
                    formData={formData as YouTubeFormData}
                    setFormData={setYouTubeFormData}
                />
            );
        case 'tiktok':
            return (
                <TikTokForm
                    formData={formData as TikTokFormData}
                    setFormData={setTikTokFormData}
                />
            );
        case 'discord':
            return (
                <DiscordForm
                    formData={formData as DiscordFormData}
                    setFormData={setDiscordFormData}
                />
            );
        case 'telegram':
            return (
                <TelegramForm
                    formData={formData as TelegramFormData}
                    setFormData={setTelegramFormData}
                />
            );
        case 'medium':
            return (
                <MediumForm
                    formData={formData as MediumFormData}
                    setFormData={setMediumFormData}
                />
            );
        case 'quora':
            return (
                <QuoraForm
                    formData={formData as QuoraFormData}
                    setFormData={setQuoraFormData}
                />
            );
        case 'slack':
            return (
                <SlackForm
                    formData={formData as SlackFormData}
                    setFormData={setSlackFormData}
                />
            );
        case 'advfn':
            return (
                <ADVFNForm
                    formData={formData as ADVFNFormData}
                    setFormData={setADVFNFormData}
                />
            );
        case 'applePodcast':
            return (
                <ApplePodcastForm
                    formData={formData as ApplePodcastFormData}
                    setFormData={setApplePodcastFormData}
                />
            );
        case 'audible':
            return (
                <AudibleForm
                    formData={formData as AudibleFormData}
                    setFormData={setAudibleFormData}
                />
            );
        case 'aussiestockforums':
            return (
                <AussieStockForumsForm
                    formData={formData as AussieStockForumsFormData}
                    setFormData={setAussieStockForumsFormData}
                />
            );
        case 'castbox':
            return (
                <CastboxForm
                    formData={formData as CastboxFormData}
                    setFormData={setCastboxFormData}
                />
            );
        case 'clubhouse':
            return (
                <ClubhouseForm
                    formData={formData as ClubhouseFormData}
                    setFormData={setClubhouseFormData}
                />
            );
        case 'iheartradio':
            return (
                <IHeartRadioForm
                    formData={formData as IHeartRadioFormData}
                    setFormData={setIHeartRadioFormData}
                />
            );
        case 'investorhub':
            return (
                <InvestorHubForm
                    formData={formData as InvestorHubFormData}
                    setFormData={setInvestorHubFormData}
                />
            );
        case 'pinterest':
            return (
                <PinterestForm
                    formData={formData as PinterestFormData}
                    setFormData={setPinterestFormData}
                />
            );
        case 'snapchat':
            return (
                <SnapchatForm
                    formData={formData as SnapchatFormData}
                    setFormData={setSnapchatFormData}
                />
            );
        case 'spotify':
            return (
                <SpotifyForm
                    formData={formData as SpotifyFormData}
                    setFormData={setSpotifyFormData}
                />
            );
        case 'stocktwits':
            return (
                <StocktwitsForm
                    formData={formData as StocktwitsFormData}
                    setFormData={setStocktwitsFormData}
                />
            );
        case 'strawman':
            return (
                <StrawmanForm
                    formData={formData as StrawmanFormData}
                    setFormData={setStrawmanFormData}
                />
            );
        case 'tradingqna':
            return (
                <TradingQnAForm
                    formData={formData as TradingQnAFormData}
                    setFormData={setTradingQnAFormData}
                />
            );
        case 'tumblr':
            return (
                <TumblrForm
                    formData={formData as TumblrFormData}
                    setFormData={setTumblrFormData}
                />
            );
        case 'vimeo':
            return (
                <VimeoForm
                    formData={formData as VimeoFormData}
                    setFormData={setVimeoFormData}
                />
            );
        case 'wechat':
            return (
                <WeChatForm
                    formData={formData as WeChatFormData}
                    setFormData={setWeChatFormData}
                />
            );
        case 'whatsapp':
            return (
                <WhatsAppForm
                    formData={formData as WhatsAppFormData}
                    setFormData={setWhatsAppFormData}
                />
            );
        case 'whirlpoolfinance':
            return (
                <WhirlpoolFinanceForm
                    formData={formData as WhirlpoolFinanceFormData}
                    setFormData={setWhirlpoolFinanceFormData}
                />
            );
        case 'bogleheads':
            return (
                <BogleheadsForm
                    formData={formData as BogleheadsFormData}
                    setFormData={setBogleheadsFormData}
                />
            );
        default:
            return null;
    }
}
