import React, { useState } from 'react';
import { Activity } from '@quarterback/types';
import ContentCard from '../../../../../../components/cards/ContentCard';
import Button from '../../../../../../components/ui/Button';
import SentimentActivityList from './SentimentActivityList';
import { FaceFrownIcon, FaceSmileIcon } from '@heroicons/react/24/outline';

interface SentimentChatterTabsProps {
    activities: Activity[];
    className?: string;
    loading?: boolean;
}

export default function SentimentChatterTabs({
    activities,
    className,
    loading
}: SentimentChatterTabsProps) {
    const [activeTab, setActiveTab] = useState<'negative' | 'positive'>('negative');

    return (
        <ContentCard
            title={
                <div className="flex gap-x-2">
                    <Button
                        variant={'tertiary'}
                        size="sm"
                        onClick={() => setActiveTab('negative')}
                        active={activeTab === 'negative'}
                        icon={<FaceFrownIcon className="size-4" />}>
                        Negative Chatter
                    </Button>
                    <Button
                        variant={'tertiary'}
                        size="sm"
                        onClick={() => setActiveTab('positive')}
                        active={activeTab === 'positive'}
                        icon={<FaceSmileIcon className="size-4" />}>
                        Positive Chatter
                    </Button>
                </div>
            }
            className={className}
            childrenClassName="!mt-0"
            loading={loading}
            initialBadge={{ hide: true }}>
            <SentimentActivityList activities={activities} sentimentType={activeTab} />
        </ContentCard>
    );
}
