import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import Tooltip from '@/components/Tooltip';
import { sentimentScore } from '@/util/sentiment';
import { CurrencyDollarIcon } from '@heroicons/react/24/outline';
import { Activity } from '@quarterback/types';
import { isDefined } from '@quarterback/util';
import classNames from 'classnames';
import { formatInTimeZone } from 'date-fns-tz';
import { useMemo } from 'react';

export default function GroupHeader({
    group,
    quote
}: {
    group: { name: string; datetime: Date; activities: Array<Activity> };
    quote: {
        current: TimeSeriesQuote | undefined;
        previous: TimeSeriesQuote | undefined;
    };
}) {
    const engagement = useMemo(() => {
        return group.activities
            .map((it) => {
                if (
                    it.type === 'tweet' ||
                    it.type === 'hotcopper' ||
                    it.type === 'linkedIn'
                ) {
                    return it.likes;
                }

                if (it.type === 'reddit' || it.type === 'redditComment') {
                    return it.score;
                }

                return undefined;
            })
            .filter(isDefined)
            .reduce((sum, engagement) => {
                return sum + engagement;
            }, 0);
    }, [group]);

    const sentiment = useMemo(() => {
        const withSentiment = group.activities
            .map((it) => it.sentiment)
            .filter(isDefined);
        const aggSentiment = withSentiment.reduce(
            (agg, sentiment) => {
                return {
                    sum: agg.sum + sentimentScore(sentiment),
                    count: agg.count + 1
                };
            },
            { sum: 0, count: 0 }
        );

        if (withSentiment.length) {
            return aggSentiment.sum / aggSentiment.count;
        } else {
            return undefined;
        }
    }, [group]);

    const [value, percentage] = useMemo(() => {
        if (quote.current && quote.previous) {
            const value = Math.log(quote.current.close / quote.previous.close);
            const percentage = Math.log(quote.current.close / quote.previous.close) * 100;
            const sign = percentage > 0 ? '+' : percentage < 0 ? '-' : '';

            return [value, `(${sign}${Math.abs(percentage).toFixed(2)}%)`];
        }
        return [];
    }, [quote]);

    const sharePriceText = useMemo(() => {
        const formatDate = (date: Date) =>
            formatInTimeZone(date, 'Australia/Sydney', 'd MMMM, yyyy');
        const formatTime = (date: Date) =>
            formatInTimeZone(date, 'Australia/Sydney', 'HH:mm');

        const groupDateString = formatDate(group.datetime);
        const todayDateString = formatDate(new Date());
        const timeString = formatTime(new Date());

        const isToday = groupDateString === todayDateString;
        const isAfter4PM = parseInt(timeString.split(':')[0], 10) >= 16;

        const isWeekend = [0, 6].includes(group.datetime.getDay());
        const isMarketClosed = isWeekend || !isToday || (isToday && isAfter4PM);

        return isMarketClosed ? 'Share price at close' : 'Current share price';
    }, [group.datetime]);

    return (
        <tr className="border-t border-qb-gray-115">
            <th
                colSpan={2}
                scope="colgroup"
                className="bg-qb-gray-50 py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                <div className="flex items-center gap-x-2 cursor-pointer text-sm font-[400] text-qb-black-100">
                    <span>{group.name}</span>
                </div>
            </th>
            <th
                colSpan={4}
                scope="colgroup"
                className="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold text-gray-900 sm:pl-3">
                <div className="flex justify-end">
                    <div className="flex items-center justify-end gap-x-2 border border-qb-gray-115 rounded-md px-2">
                        {quote.current && (
                            <>
                                <Tooltip text={sharePriceText}>
                                    <div className="flex items-center justify-end gap-x-2 cursor-help">
                                        <span className="text-xs text-qb-gray-150">
                                            <CurrencyDollarIcon className="size-4" />
                                        </span>
                                        <span className={classNames('text-xs')}>
                                            ${(quote?.current?.close ?? 0).toFixed(4)}
                                        </span>
                                        <span
                                            className={classNames('text-xs font-[400]', {
                                                'text-red-600': value && value < 0,
                                                'text-green-600': value && value > 0
                                            })}>
                                            {Math.abs(value ?? 0).toFixed(2)}
                                        </span>
                                        <span
                                            className={classNames(
                                                'text-xs font-[400]',
                                                {
                                                    '!bg-qb-red-50 text-red-600':
                                                        value && value < 0,
                                                    'text-green-600 !bg-qb-green-50':
                                                        value && value > 0
                                                },
                                                'bg-white py-1 px-2 rounded-md'
                                            )}>
                                            {percentage}
                                        </span>
                                    </div>
                                </Tooltip>

                                <Tooltip text={'Volume traded'}>
                                    <div className="flex items-center justify-end gap-x-2 cursor-help ml-3 text-qb-gray-150 font-medium text-xs">
                                        <span className="text-xs">#</span>
                                        <span
                                            className={classNames(
                                                'font-semibold text-xs'
                                            )}>
                                            {quote?.current?.volume.toLocaleString()}
                                        </span>
                                    </div>
                                </Tooltip>
                            </>
                        )}
                    </div>
                </div>
            </th>
        </tr>
    );
}
