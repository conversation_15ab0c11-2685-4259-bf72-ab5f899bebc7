import Checkbox from '@/components/ui/Checkbox';
import React from 'react';

export default function ActivitiesHeader() {
    return (
        <thead>
            <tr className="text-sm text-gray-900">
                <th className="w-10 border border-qb-gray-115 border-r-transparent">
                    <Checkbox checked={false} />
                </th>
                <th className="border border-qb-gray-115 px-4 py-2 text-left">Source</th>
                <th className="border border-qb-gray-115 px-4 py-2 text-left">Body</th>
                <th className="text-sm border border-qb-gray-115 px-4 py-2 text-right">
                    Time
                </th>
                <th className="border border-qb-gray-115 px-4 py-2 text-right">
                    Sentiment
                </th>
                <th className="border border-qb-gray-115 px-6 py-2 text-right">Format</th>
            </tr>
        </thead>
    );
}
