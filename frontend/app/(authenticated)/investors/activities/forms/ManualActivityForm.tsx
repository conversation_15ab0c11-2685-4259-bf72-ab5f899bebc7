import { useActivityForm } from '@/app/(authenticated)/investors/activities/contexts/ActivityFormProvider';
import ManualActivityFormFactory from '@/app/(authenticated)/investors/activities/forms/ManualActivityFormFactory';

export default function ManualActivityForm() {
    const { isFormOpen, closeForm, onSubmit } = useActivityForm();

    const handleSubmit = async (data: any) => {
        if (onSubmit) {
            return await onSubmit(data);
        }
        return Promise.resolve();
    };

    return (
        <ManualActivityFormFactory
            addingActivity={isFormOpen}
            setAddingActivity={(open: boolean) => {
                if (!open) {
                    closeForm();
                }
            }}
            onSubmit={handleSubmit}
        />
    );
}
