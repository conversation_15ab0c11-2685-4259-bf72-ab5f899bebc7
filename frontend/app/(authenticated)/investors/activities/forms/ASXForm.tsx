import { ASXFormData, ASXFormData as ASXFormSchema } from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';
import { CheckboxField } from '@/components/forms/fields/CheckboxField';

interface ASXFormProps {
    formData: ASXFormData;
    setFormData: (data: ASXFormData) => void;
}

export default function ASXForm({ formData, setFormData }: ASXFormProps) {
    const { getFieldError, validateField } = useFormValidation(ASXFormSchema, formData);

    const updateField = useCallback(
        (field: keyof ASXFormData, value: string | boolean | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof ASXFormData) => {
            validateField(field);
        },
        [validateField]
    );

    return (
        <div className="space-y-4 mt-4">
            <TextField
                label="Title"
                value={formData.title || ''}
                onChange={(value) => updateField('title', value)}
                onBlur={() => handleBlur('title')}
                placeholder="Announcement Title"
                error={getFieldError('title')}
            />

            <TextArea
                label="Announcement Content"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://www.asx.com.au/..."
                error={getFieldError('url')}
            />

            <CheckboxField
                label="Price Sensitive"
                value={formData.sensitive || false}
                onChange={(value) => updateField('sensitive', value)}
                error={getFieldError('sensitive')}
            />
        </div>
    );
}
