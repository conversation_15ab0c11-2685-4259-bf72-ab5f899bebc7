// components/ActivitiesLayoutContext.tsx
import { createContext, useContext } from 'react';

const ActivitiesLayoutContext = createContext<{
    isRightPanelExpanded: boolean;
    setIsRightPanelExpanded: (val: boolean) => void;
}>({
    isRightPanelExpanded: false,
    setIsRightPanelExpanded: () => {}
});

export const useActivitiesLayout = () => useContext(ActivitiesLayoutContext);

export default ActivitiesLayoutContext;
