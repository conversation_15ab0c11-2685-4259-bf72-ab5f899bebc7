'use client';

import { useState } from 'react';
import ActivitiesLayoutContext from './activities/ActivityLayoutContext';

export default function InvestorsLayout({ children }: { children: React.ReactNode }) {
    const [isRightPanelExpanded, setIsRightPanelExpanded] = useState(false);

    return (
        <ActivitiesLayoutContext.Provider
            value={{
                isRightPanelExpanded,
                setIsRightPanelExpanded
            }}>
            {children}
        </ActivitiesLayoutContext.Provider>
    );
}
