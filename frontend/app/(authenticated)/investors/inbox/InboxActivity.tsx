import { Activity } from '@quarterback/types';
import useActivitySummary from '@/util/useActivitySummary';
import React, { useMemo } from 'react';
import { format, isBefore, subMonths } from 'date-fns';
import { DiscreteSentiment, discreteSentiment, sentimentScore } from '@/util/sentiment';
import Link from 'next/link';
import classNames from 'classnames';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import { FlagIcon } from '@heroicons/react/24/solid';
import { ArrowTurnDownRightIcon } from '@heroicons/react/24/outline';

export default function InboxActivity({
    activity,
    selected
}: {
    activity: Activity;
    selected: boolean;
}) {
    const { title, description, author } = useActivitySummary(activity);

    const date = useMemo(() => {
        if (isBefore(activity.posted, subMonths(new Date(), 12))) {
            return format(activity.posted, 'd MMM, yyyy');
        } else {
            return format(activity.posted, 'd MMM');
        }
    }, [activity]);

    const showThreadIcon = useMemo(() => {
        return (
            activity?.id &&
            activity.type === 'hotcopper' &&
            activity.url.includes('page-')
        );
    }, [activity]);

    const sentiment = useMemo(() => {
        return (activity.type === 'media' ||
            activity.type === 'tweet' ||
            activity.type === 'linkedIn' ||
            activity.type === 'hotcopper') &&
            activity.sentiment !== undefined
            ? discreteSentiment(sentimentScore(activity.sentiment))
            : undefined;
    }, [activity]);

    return (
        <li key={activity.id}>
            <Link
                href={`/investors/inbox/${activity.id}`}
                className={classNames(
                    'relative flex gap-x-3 py-4 lg:px-4 rounded-lg border-r-4',
                    {
                        'bg-indigo-600/90 hover:bg-indigo-600/95': selected,
                        'bg-white hover:bg-indigo-100': !selected && !activity.read,
                        'bg-gray-50 hover:bg-indigo-100': !selected && activity.read,
                        'border-r-4 border-red-500':
                            sentiment === DiscreteSentiment.NEGATIVE,
                        'border-r-4 border-yellow-500':
                            sentiment === DiscreteSentiment.LACKING,
                        'border-r-4 border-green-500':
                            sentiment === DiscreteSentiment.POSITIVE,
                        'border-r-4 border-gray-500/0':
                            sentiment === DiscreteSentiment.NEUTRAL || !sentiment
                    }
                )}>
                <div className="flex flex-col flex-shrink-0 justify-center items-center gap-y-2">
                    <div className="relative">
                        <ActivitySourceIcon
                            activity={activity}
                            className={classNames(
                                'h-6 w-6 flex-none rounded-full bg-gray-50 object-cover',
                                {
                                    'opacity-60': !selected && activity.read
                                }
                            )}
                        />
                        {!activity.read ? (
                            <div className="absolute -top-1 -right-1 size-3 rounded-full bg-indigo-600 border-2 border-white" />
                        ) : null}
                    </div>
                </div>

                <div className="flex flex-col flex-1 min-w-0">
                    <span
                        className={classNames('text-xs font-medium', {
                            'text-white': selected,
                            'text-gray-400': !selected && activity.read
                        })}>
                        {author}
                    </span>
                    <p
                        className={classNames(
                            'text-sm font-medium truncate flex gap-r-1',
                            {
                                'text-gray-900': !selected && !activity.read,
                                'text-gray-400': !selected && activity.read,
                                'text-gray-50': selected
                            }
                        )}>
                        {showThreadIcon ? (
                            <ArrowTurnDownRightIcon
                                className={classNames('size-5', {
                                    'text-gray-900': !selected && !activity.read,
                                    'text-gray-400': !selected && activity.read,
                                    'text-gray-50': selected
                                })}
                            />
                        ) : null}
                        {title}
                    </p>

                    {description && (
                        <p
                            className={classNames('mt-1 text-xs line-clamp-1', {
                                'text-gray-500': !selected && !activity.read,
                                'text-gray-400': !selected && activity.read,
                                'text-gray-100': selected
                            })}>
                            {description}
                        </p>
                    )}
                </div>
                <div className="hidden flex-shrink-0 sm:flex sm:flex-col sm:items-end gap-y-1">
                    <p
                        className={classNames('mt-1 text-xs leading-5', {
                            'text-gray-500': !selected,
                            'text-gray-100': selected
                        })}>
                        <time dateTime={activity.posted.toString()}>{date}</time>
                    </p>
                    {activity.flagged ? (
                        <FlagIcon className="size-3 text-red-500" />
                    ) : null}
                </div>
            </Link>
        </li>
    );
}
