import ContentCard from '@/components/cards/ContentCard';
import InitialColors from '@/util/InitialColors';
import { useSentimentBands } from '@/util/sentiment';
import { ArrowDownIcon, ArrowUpIcon, ChevronRightIcon } from '@heroicons/react/16/solid';
import { Activity, Followers } from '@quarterback/types';
import { isDefined } from '@quarterback/util';
import classNames from 'classnames';
import React, { useMemo } from 'react';


const DifferenceIndicator: React.FC<{ previousValue: number, currentValue: number }> = ({ previousValue, currentValue }) => {
    const difference = currentValue - previousValue;
    let percent = 0;
    if (difference !== 0) {
        percent = (difference / previousValue || 1) * 100;
    }
    if (percent !== 0) {
        const formatPercent = Number.isInteger(percent) ? `${percent}%` : `${percent.toFixed(2)}%`
        return <div
            className={classNames('flex items-center text-xs mt-2', {
                'text-red-600': percent < 0,
                'text-green-600': percent > 0
            })}>
            {percent >= 0 ? '+' : ''}{formatPercent} vs previous
        </div>
    }
    return null
}

export default function Chatter({
    media,
    chatter,
    followers,
    className,
    previousData
}: {
    media: Array<Activity>;
    chatter: Array<Activity>;
    followers: Followers;
    className?: string;
    previousData: { activitiesCount: number; mediaCount: number; peopleCount: number }
}) {
    const authors = useMemo(() => {
        return new Set(
            chatter
                .map((activity) => {
                    switch (activity.type) {
                        case 'hotcopper':
                        case 'tweet':
                        case 'reddit':
                        case 'redditComment':
                            return activity.author?.userId;
                        case 'linkedIn':
                            return activity.author?.name;
                        case 'media':
                            return activity.source.name;
                        default:
                            return undefined;
                    }
                })
                .filter(isDefined)
        );
    }, [chatter]);

    const { now: currentFollowers, previous: previousFollowers } = useMemo(() => {
        return {
            now:
                +(followers.twitter?.[0]?.followers ?? 0) +
                +(followers.linkedIn?.[0]?.followers ?? 0),
            previous:
                +(
                    followers?.twitter?.[1]?.followers ??
                    followers?.twitter?.[0]?.followers ??
                    0
                ) +
                +(
                    followers?.linkedIn?.[1]?.followers ??
                    followers?.linkedIn?.[0]?.followers ??
                    0
                )
        };
    }, [followers?.linkedIn, followers?.twitter]);

    const followersChange = useMemo(() => {
        const difference = currentFollowers - previousFollowers;
        if (difference !== 0) {
            return (difference / previousFollowers || 1) * 100;
        }

        return 0;
    }, [currentFollowers, previousFollowers]);

    const peopleChange = useMemo(() => {
        if (!previousData.peopleCount) return 0;
        const previousCount = Number(previousData.peopleCount);
        const currentCount = authors?.size ?? 0;
        const difference = currentCount - previousCount
        if (difference !== 0) {
            return (difference / (previousFollowers || 1)) * 100;
        }
        return 0;

    }, [previousData.peopleCount, authors])


    return (
        <div className={classNames('grid grid-cols-6 gap-4 ')}>
            <ContentCard
                className="col-span-1"
                title="Activities"
                initialBadge={{
                    color: InitialColors.ACTIVITY
                }}>
                <div className="text-3xl">{chatter.length}</div>
                <DifferenceIndicator previousValue={previousData.activitiesCount} currentValue={chatter.length} />
            </ContentCard>
            <ContentCard
                className="col-span-1"
                title="Media"
                initialBadge={{
                    color: InitialColors.MEDIA
                }}>
                <div className="text-3xl">{media.length}</div>
                <DifferenceIndicator previousValue={previousData.mediaCount} currentValue={media.length} />

            </ContentCard>
            <ContentCard
                title="People"
                initialBadge={{
                    color: InitialColors.PEOPLE
                }}>
                <div className="text-3xl">{authors.size}</div>
                <DifferenceIndicator previousValue={previousData.peopleCount} currentValue={authors.size} />

            </ContentCard>

            <ContentCard className="col-span-1 col-start-6" title={<div className='py-2'>Followers</div>}>
                <div className="text-3xl font-medium">{currentFollowers}</div>
                <DifferenceIndicator previousValue={previousFollowers} currentValue={currentFollowers} />
            </ContentCard>
        </div>
    );
}
