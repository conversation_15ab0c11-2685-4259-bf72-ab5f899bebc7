'use client';

import useActivitiesMutation from '@/api/hooks/admin/mutations/useActivitiesMutation';
import useActivities from '@/api/hooks/useActivities';
import useBroadcasts from '@/api/hooks/useBroadcasts';
import useChatter from '@/api/hooks/useChatter';
import useFollowers from '@/api/hooks/useFollowers';
import useMedia from '@/api/hooks/useMedia';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import Chatter from '@/app/(authenticated)/investors/dashboard/Chatter';
import Metrics from '@/app/(authenticated)/investors/dashboard/Metrics';
import Sidebar from '@/app/(authenticated)/investors/dashboard/Sidebar';
import DateRangePicker from '@/components/DateRangePicker';
import { useOrganisation } from '@/components/OrganisationProvider';
import { ActivityFormProvider, NewActivitySection } from '@/components/activity';
import ContentCard from '@/components/cards/ContentCard';
import SourceSentiments from '@/components/charts/SourceSentiments';
import ActivityPie from '@/components/charts/activity-pie';
import useChatterStats from '@/hooks/useChatterStats';
import InitialColors from '@/util/InitialColors';
import { Activity2 } from '@quarterback/types';
import { startOfDay, sub } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import SharePriceVsActivity from './SharePricevsActivity';

export default function Dashboard() {
    const router = useRouter();

    const [range, setRange] = useState<DateRange>({
        from: startOfDay(sub(new Date(), { months: 1 })),
        to: startOfDay(new Date())
    });

    const organisation = useOrganisation();

    const { data: activities = [] } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: followers = { twitter: [], linkedIn: [] } } = useFollowers(
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: timeSeries } = useTimeSeries(
        organisation?.selected
            ? {
                  symbol: organisation.selected.entity.symbol,
                  exchange: organisation.selected.entity.exchange
              }
            : undefined,
        range.from!,
        range.to!
    );

    const broadcasts = useBroadcasts(activities);
    const chatter = useChatter(activities);
    const media = useMedia(activities);

    const { previous } = useChatterStats(range);
    const { trigger } = useActivitiesMutation();

    function handleActivityChartClick(datetime: string) {
        router.push(`/investors/activities?from=${datetime}&to=${datetime}`);
    }
    async function addManualActivity(data: Activity2[]) {
        const result = await trigger(data);
        return result;
    }
    return (
        <ActivityFormProvider onSubmit={addManualActivity}>
            <div>
                <div className="flex items-center justify-between border-b border-gray-200 p-4">
                    <DateRangePicker range={range} setRange={setRange} required />
                    <NewActivitySection />
                </div>
                <div className="py-4 flex flex-col gap-4">
                    <div className="flex lg:flex-row flex-col gap-4 px-4">
                        <div className="flex flex-col flex-1 gap-2">
                            <div className="col-span-8">
                                <Chatter
                                    media={media}
                                    chatter={chatter}
                                    followers={followers}
                                    previousData={previous}
                                    className="p-4 bg-white border rounded-md"
                                />
                            </div>
                            <div className="col-span-8">
                                <SharePriceVsActivity
                                    organisation={organisation?.selected?.organisation}
                                    entity={organisation?.selected?.entity}
                                    range={range}
                                    onClick={handleActivityChartClick}
                                />
                            </div>
                        </div>
                        <div className="items-stretch w-full lg:w-1/3">
                            <Sidebar />
                        </div>
                    </div>
                    <div className="flex flex-wrap lg:flex-nowrap  items-stretch gap-4 px-4">
                        <ContentCard
                            className="w-[20%]"
                            title={<div className="py-2">Sentiment vs Source</div>}>
                            <SourceSentiments activities={activities} />
                        </ContentCard>

                        <ContentCard
                            className="w-[20%]"
                            title="Activities by Format"
                            initialBadge={{
                                color: InitialColors.ACTIVITY
                            }}>
                            <ActivityPie
                                entity={organisation.selected?.entity}
                                activities={activities}
                                groupBy="format"
                            />
                        </ContentCard>
                        <ContentCard
                            className="w-[20%]"
                            title="Activities by Source"
                            initialBadge={{
                                color: InitialColors.ACTIVITY
                            }}>
                            <ActivityPie
                                entity={organisation.selected?.entity}
                                activities={activities}
                                groupBy="source"
                            />
                        </ContentCard>

                        <Metrics className="w-[40%]" from={range.from!} to={range.to!} />
                    </div>
                </div>
            </div>
        </ActivityFormProvider>
    );
}
