import useRisk from '@/api/hooks/useRisk';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import ContentCard from '@/components/cards/ContentCard';
import AbnormalReturnsChart from '@/components/charts/abnormal-return';
import useCombinedQuotes from '@/components/hooks/useCombinedQuotes';
import { useOrganisation } from '@/components/OrganisationProvider';
import { ParentSize } from '@visx/responsive';

interface Props {
    className?: string;
    from: Date;
    to: Date;
}

export default function Metrics({ className, from, to }: Props) {
    const organisation = useOrganisation();

    const { data: timeSeries } = useTimeSeries(organisation?.selected?.entity, from, to);
    const { data: indexSeries } = useTimeSeries(
        { symbol: 'AXJO', exchange: 'ASX' },
        from,
        to
    );

    const { data: risk } = useRisk(organisation?.selected?.entity, {
        symbol: 'AXJO',
        exchange: 'ASX'
    });

    const combinedQuotes = useCombinedQuotes(indexSeries, timeSeries);

    // values used preeviously
    // const sharePriceChange = useMemo(() => {
    //     if (timeSeries && timeSeries.values.length) {
    //         const firstClose = timeSeries.values[0].close;
    //         const lastClose = timeSeries.values[timeSeries.values.length - 1].close;

    //         return (lastClose / firstClose - 1) * 100;
    //     } else {
    //         return undefined;
    //     }
    // }, [timeSeries]);

    // const sharePrice = useMemo(() => {
    //     if (timeSeries) {
    //         return timeSeries.values[timeSeries.values.length - 1].close;
    //     } else {
    //         return undefined;
    //     }
    // }, [timeSeries]);

    // const cumAbnormalReturns = useMemo((): number | undefined => {
    //     if (!risk || combinedQuotes.length < 2) return undefined; // Ensure valid data

    //     return combinedQuotes.reduce((cum, { index, stock }, i) => {
    //         if (i === 0) return cum; // Skip first entry since there's no previous day

    //         const prevQuote = combinedQuotes[i - 1];

    //         const stockReturn = Math.log(stock.close / prevQuote.stock.close);
    //         const indexReturn = Math.log(index.close / prevQuote.index.close);

    //         return cum + (stockReturn - (risk.intercept + risk.slope * indexReturn));
    //     }, 0);
    // }, [combinedQuotes, risk]);

    return (
        <ContentCard className={className} title={<div className='py-2'>Share Price vs Index Price</div>}>
            <div className="h-[200px]">
                {risk && (
                    <ParentSize>
                        {({ width, height }) => (
                            <AbnormalReturnsChart
                                width={width}
                                height={height}
                                risk={risk}
                                quotes={combinedQuotes}
                            />
                        )}
                    </ParentSize>
                )}
            </div>
            <div className="flex gap-x-3 mt-2">
                <div className="flex items-center gap-x-1">
                    <div
                        className="w-2 h-2 rounded-full bg-indigo-200"
                        style={{ background: '#63ABFD' }}
                    />
                    <span className="text-xs text-gray-400">Share price</span>
                </div>
                <div className="flex items-center gap-x-1">
                    <div
                        className="w-2 h-2 rounded-full"
                        style={{ background: '#A155B9' }}
                    />
                    <span className="text-xs text-gray-400">Abnormal return</span>
                </div>
            </div>
        </ContentCard>
    );
}
