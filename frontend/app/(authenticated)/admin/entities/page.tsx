'use client';

import React, { FormEvent, useState } from 'react';

import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild
} from '@headlessui/react';

import useEntities from '@/api/hooks/admin/useEntities';
import { ListedEntity } from '@quarterback/types';
import useEntitiesMutation from '@/api/hooks/admin/mutations/useEntitiesMutation';

function AddEntityForm({
    entity,
    onSubmit
}: {
    entity?: ListedEntity;
    onSubmit: () => void;
}) {
    const { trigger } = useEntitiesMutation();

    const [name, setName] = useState(entity?.name ?? '');
    const [symbol, setSymbol] = useState(entity?.symbol ?? '');
    const [exchange, setExchange] = useState(entity?.exchange ?? 'ASX');
    const [twitterUsername, setTwitterUsername] = useState(entity?.twitterUsername ?? '');
    const [twitterQuery, setTwitterQuery] = useState(entity?.twitterQuery ?? '');
    const [newsQuery, setNewsQuery] = useState(entity?.newsQuery ?? '');
    const [redditQuery, setRedditQuery] = useState(entity?.redditQuery ?? '');
    const [linkedInQuery, setlinkedInQuery] = useState(entity?.linkedInQuery ?? '');
    const [linkedInUsername, setLinkedInUsername] = useState(
        entity?.linkedInUsername ?? ''
    );
    const [about, setAbout] = useState(entity?.about ?? '');

    async function handleSubmit(event: FormEvent) {
        event.preventDefault();

        await trigger([
            {
                name,
                symbol,
                exchange,
                twitterUsername,
                twitterQuery,
                newsQuery,
                redditQuery,
                linkedInQuery,
                linkedInUsername,
                about
            }
        ]);

        onSubmit();
    }

    return (
        <form id="add-entity" onSubmit={handleSubmit}>
            <div className="space-y-12">
                <div className="border-b border-gray-900/10 pb-12">
                    <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                        <div className="sm:col-span-full">
                            <label
                                htmlFor="name"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Name*
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="name"
                                    id="name"
                                    placeholder="e.g. Veem Pty Ltd"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="symbol"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Ticker symbol*
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="symbol"
                                    id="symbol"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={symbol}
                                    onChange={(e) => setSymbol(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="exchange"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Exchange*
                            </label>
                            <div className="mt-2">
                                <select
                                    id="exchange"
                                    name="exchange"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6"
                                    value={exchange}
                                    disabled>
                                    <option value="ASX">ASX</option>
                                </select>
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="twitter-username"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Twitter username
                            </label>
                            <div className="mt-2 flex rounded-md shadow-sm">
                                <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 px-3 text-gray-500 sm:text-sm">
                                    @
                                </span>
                                <input
                                    type="text"
                                    name="twitter-username"
                                    id="twitter-username"
                                    className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    placeholder="e.g. veem"
                                    value={twitterUsername}
                                    onChange={(e) => setTwitterUsername(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="twitter-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Twitter search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="twitter-search-term"
                                    id="twitter-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={twitterQuery}
                                    onChange={(e) => setTwitterQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="linkedIn-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                LinkedIn search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="linkedIn-search-term"
                                    id="linkedIn-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={linkedInQuery}
                                    onChange={(e) => setlinkedInQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="linkedIn-username"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                LinkedIn username
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="linkedIn-username"
                                    id="linkedIn-username"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={linkedInUsername}
                                    onChange={(e) => setLinkedInUsername(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="news-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                News search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="news-search-term"
                                    id="news-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={newsQuery}
                                    onChange={(e) => setNewsQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="news-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Reddit search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="news-search-term"
                                    id="news-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={redditQuery}
                                    onChange={(e) => setRedditQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-full">
                            <label
                                htmlFor="about"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                About
                            </label>
                            <div className="mt-2">
                                <textarea
                                    name="about"
                                    id="about"
                                    placeholder="About the company"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={about}
                                    onChange={(e) => setAbout(e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    );
}

function AddEntityModal({
    open,
    setOpen,
    entity
}: {
    open: boolean;
    setOpen: (value: boolean) => void;
    entity?: ListedEntity;
}) {
    function handleSubmit() {
        setOpen(false);
    }

    return (
        <Transition show={open}>
            <Dialog className="relative z-50" onClose={setOpen}>
                <TransitionChild
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0">
                    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                </TransitionChild>

                <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <TransitionChild
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                            <DialogPanel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                                <DialogTitle
                                    as="h3"
                                    className="text-base font-semibold leading-6 text-gray-900">
                                    Add Exchange Listed Entity
                                </DialogTitle>
                                <AddEntityForm entity={entity} onSubmit={handleSubmit} />
                                <div className="mt-6 flex items-center justify-end gap-x-6">
                                    <button
                                        type="button"
                                        className="text-sm font-semibold leading-6 text-gray-900"
                                        onClick={() => setOpen(false)}>
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        form="add-entity"
                                        className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                        Save
                                    </button>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}

export default function ListedEntities() {
    const { data: entities } = useEntities();
    const [modalOpen, setModalOpen] = useState(false);
    const [editing, setEditing] = useState<ListedEntity | undefined>(undefined);

    return (
        <div className="p-4 sm:p-6 lg:p-8">
            <div className="flex items-center">
                <div className="flex-auto">
                    <h1 className="text-base font-semibold leading-6 text-gray-900">
                        Listed entities
                    </h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Exchange listed entities for which we collect data and insights.
                    </p>
                </div>
                <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                    <button
                        type="button"
                        onClick={() => {
                            setEditing(undefined);
                            setModalOpen(true);
                        }}
                        className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Add entity
                    </button>
                </div>
            </div>
            <AddEntityModal open={modalOpen} setOpen={setModalOpen} entity={editing} />
            <div className="mt-8 w-full overflow-x-auto">
                <div className="inline-block min-w-full align-middle">
                    <table className="min-w-full table-fixed divide-y divide-gray-300">
                        <thead>
                            <tr>
                                <th className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0 w-40 break-words">
                                    Name
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-24 break-words">
                                    Symbol
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-24 break-words">
                                    Exchange
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    Twitter username
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    Twitter search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    LinkedIn search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    LinkedIn Username
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    News search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    Reddit search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-64 break-words">
                                    About
                                </th>
                                <th className="relative py-3.5 pl-3 pr-4 sm:pr-0 w-16">
                                    <span className="sr-only">Edit</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {(entities ?? []).map((entity) => (
                                <tr key={`${entity.symbol}:${entity.exchange}`}>
                                    <td className="whitespace-normal break-words py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                        {entity.name}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.symbol}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.exchange}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.twitterUsername}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.twitterQuery}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.linkedInQuery}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.linkedInUsername}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.newsQuery}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.redditQuery}
                                    </td>
                                    <td className="px-3 py-4 text-sm text-gray-500">
                                        <div className="line-clamp-2 break-words">
                                            {entity.about}
                                        </div>
                                    </td>
                                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                        <button
                                            onClick={() => {
                                                setEditing(entity);
                                                setModalOpen(true);
                                            }}
                                            className="text-indigo-600 hover:text-indigo-900">
                                            Edit
                                            <span className="sr-only">
                                                , {entity.name}
                                            </span>
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
