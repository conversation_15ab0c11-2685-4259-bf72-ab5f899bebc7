import React, { useCallback, useState } from 'react';
import { Activity2 } from '@quarterback/types';
import classNames from 'classnames';
import { DocumentTextIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { ChevronRightIcon } from '@heroicons/react/16/solid';
import { Readable } from 'stream';
import * as csv from '@fast-csv/parse';
import { parse as parseDate } from 'date-fns';

function garbageFire(stream: ReadableStream<Uint8Array>): Readable {
    const reader = stream.getReader();

    return new Readable({
        async read() {
            try {
                const { done, value } = await reader.read();
                if (done) {
                    this.push(null); // Signal the end of the stream
                } else {
                    this.push(Buffer.from(value)); // Push chunks as Buffers
                }
            } catch (error) {
                if (error instanceof Error) {
                    this.destroy(error);
                } else {
                    this.destroy(new Error(String(error)));
                }
            }
        }
    });
}

interface NewsRow {
    Title: string;
    URL: string;
    Content: string;
    'Image URL': string;
    'Source Domain': string;
    'Source Name': string;
    Datetime: string;
}

export default function Upload({
    setActivities
}: {
    setActivities: React.Dispatch<React.SetStateAction<Array<Activity2>>>;
}) {
    const [over, setOver] = useState(false);
    const [file, setFile] = useState<File | undefined>();
    const [symbol, setSymbol] = useState('');

    const handleFileInputChange = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const files = Array.from(event.target.files ?? []);

            for (const file of files) {
                setFile(file);
            }
        },
        [setFile]
    );

    const handleDrop = useCallback(
        (event: React.DragEvent) => {
            const items = Array.from(event.dataTransfer?.items || []);

            setOver(false);

            if (
                !file &&
                items.length === 1 &&
                items.every((it) => it.kind === 'file' && it.type === 'text/csv')
            ) {
                event.preventDefault();

                for (const item of items) {
                    setFile(item.getAsFile() ?? undefined);
                }
            }
        },
        [file, setOver]
    );

    const handleDragOver = useCallback(
        (event: React.DragEvent) => {
            const items = Array.from(event.dataTransfer?.items || []);

            if (
                !file &&
                items.length === 1 &&
                items.every((it) => it.kind === 'file' && it.type === 'text/csv')
            ) {
                event.preventDefault();
                setOver(true);
            } else {
                setOver(false);
            }
        },
        [setOver, file]
    );

    const handleDragLeave = useCallback(() => setOver(false), [setOver]);

    const handleSymbolChange = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            if (event.target.value.match(/^[A-Z0-9]{0,3}$/)) {
                setSymbol(event.target.value);
            }
        },
        []
    );

    const handleSubmit = useCallback(async () => {
        if (file && symbol.length === 3) {
            const activities: Array<Activity2> = [];
            const stream = csv
                .parseStream<
                    NewsRow,
                    Activity2
                >(garbageFire(file.stream()), { headers: true })
                .transform((row: NewsRow): Activity2 => {
                    if (!row['URL'])
                        throw new Error(`Activities must have a URL. ${row['Title']}.`);

                    return {
                        title: row['Title'].trim(),
                        posted: parseDate(row['Datetime'], 'yyyy-MM-dd H:mm', new Date()),
                        url: row['URL'].trim(),
                        body: row['Content'].trim(),
                        media: {
                            ...(row['Image URL'] ? { image: row['Image URL'] } : {}),
                            source: {
                                name: row['Source Name'].trim(),
                                domain: row['Source Domain'].trim()
                            }
                        },
                        symbol,
                        exchange: 'ASX'
                    };
                });

            for await (const activity of stream) {
                activities.push(activity);
            }

            setActivities(activities);
        }
    }, [file, symbol, setActivities]);

    return (
        <div>
            <div className="my-4 sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="text-base font-semibold leading-6 text-gray-900">
                        1. Upload CSV
                    </h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Drop a CSV in the box below to import
                    </p>
                </div>
            </div>
            <div className="mt-2">
                <label
                    htmlFor="first-name"
                    className="block text-sm/6 font-medium text-gray-900">
                    CSV
                </label>
                <div
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    className={classNames(
                        'mt-2 flex justify-center rounded-lg border border-dashed px-6 py-10',
                        { 'border-gray-900/25': !over, 'border-gray-900/50': over }
                    )}>
                    <div className="text-center relative">
                        {file ? (
                            <div
                                onClick={() => setFile(undefined)}
                                className="absolute top-0 right-0 cursor-pointer rounded-full bg-indigo-600 text-white p-0.5">
                                <XMarkIcon className="size-3" />
                            </div>
                        ) : null}
                        <DocumentTextIcon
                            aria-hidden="true"
                            className="mx-auto h-12 w-12 text-gray-300"
                        />
                        <div className="mt-4 flex text-sm/6 text-gray-600">
                            {file ? (
                                <p>{file.name}</p>
                            ) : (
                                <>
                                    <label
                                        htmlFor="file-upload"
                                        className="relative cursor-pointer rounded-md bg-white font-semibold text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-indigo-500">
                                        <span>Upload a file</span>
                                        <input
                                            id="file-upload"
                                            name="file-upload"
                                            type="file"
                                            className="sr-only"
                                            accept="text/csv"
                                            onChange={handleFileInputChange}
                                        />
                                    </label>
                                    <p className="pl-1">or drag and drop</p>
                                </>
                            )}
                        </div>
                        {!file ? <p className="text-xs/5 text-gray-600">.CSV</p> : null}
                    </div>
                </div>
            </div>
            <div className="mt-2">
                <label
                    htmlFor="first-name"
                    className="block text-sm/6 font-medium text-gray-900">
                    Symbol
                </label>
                <div className="mt-2">
                    <input
                        id="symbol"
                        name="symbol"
                        type="text"
                        placeholder="e.g. VEE"
                        maxLength={3}
                        value={symbol}
                        onChange={handleSymbolChange}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
                    />
                </div>
            </div>
            <button
                type="button"
                disabled={!file || symbol.length !== 3}
                onClick={handleSubmit}
                className="mt-2 w-full flex flex-row items-center justify-center gap-x-2 rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm disabled:bg-indigo-500 hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                <span>2. Preview</span>
                <ChevronRightIcon className="size-4" />
            </button>
        </div>
    );
}
