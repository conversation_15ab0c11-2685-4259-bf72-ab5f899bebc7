'use client';

import { useOrganisation } from '@/components/OrganisationProvider';
import Button from '@/components/ui/Button';
import Alert from '@/components/Alert';
import { EntityUpdateForm } from './EntityUpdateForm';
import { useState } from 'react';

export default function CompanySettings() {
    const { selected: { entity, organisation } = {} } = useOrganisation();
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');

    if (!entity || !organisation) {
        return (
            <div className="px-32">
                <div className="text-center py-12">
                    <p className="text-gray-500">
                        Please select an entity to configure settings.
                    </p>
                </div>
            </div>
        );
    }

    function handleSubmit(success: boolean, msg?: string) {
        if (success) {
            setMessage(msg || 'Entity updated successfully');
            setError('');
        } else {
            setError(msg || 'Failed to update entity');
            setMessage('');
        }
    }

    return (
        <div className="px-32">
            {error || message ? (
                <div className="mb-6">
                    {error ? (
                        <Alert status="error" text={error} dismiss={() => setError('')} />
                    ) : null}
                    {message ? (
                        <Alert
                            status="success"
                            text={message}
                            dismiss={() => setMessage('')}
                        />
                    ) : null}
                </div>
            ) : null}

            <EntityUpdateForm
                entity={entity}
                organisation={organisation}
                onSubmit={handleSubmit}
            />

            <div className="mb-4">
                <Button variant="primary" type="submit" form="update-entity" size="sm">
                    Save Changes
                </Button>
            </div>
        </div>
    );
}
