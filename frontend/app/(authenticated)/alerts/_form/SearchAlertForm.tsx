import React from 'react';
import { AlertThreshold, searchTermRelevanceText } from '@quarterback/types';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';
import classNames from 'classnames';

const options = [
    { name: `${searchTermRelevanceText.get(0.48)} relevant`, value: 0.48 },
    { name: `${searchTermRelevanceText.get(0.41)} relevant`, value: 0.41 },
    { name: `${searchTermRelevanceText.get(0.3)} relevant`, value: 0.3 }
];

export default function SearchAlertForm({
    threshold,
    setThreshold,
    searchTerm,
    setSearchTerm
}: {
    threshold: AlertThreshold;
    searchTerm: string;
    setThreshold: React.Dispatch<React.SetStateAction<AlertThreshold>>;
    setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
}) {
    return (
        <>
            <span className="text-qb-gray-150 text-sm">Keywords</span>
            <div className="flex items-center gap-2 text-qb-black-100">
                <div className="inline-flex items-center border border-qb-gray-105 rounded-lg bg-white overflow-hidden ">
                    <span className="px-3 whitespace-nowrap">Activity is</span>

                    <Listbox
                        value={threshold?.threshold}
                        onChange={(val) =>
                            setThreshold({
                                ...threshold,
                                threshold: val
                            })
                        }>
                        {({ open }) => (
                            <div className="relative border-l border-qb-gray-105">
                                <ListboxButton className="relative cursor-default rounded-r-md bg-qb-gray-40 py-1.5 px-3 text-left border-none focus:ring-0 outline-none w-fit">
                                    {options.find((o) => o.value === threshold?.threshold)
                                        ?.name || (
                                            <span className="text-gray-500">Select</span>
                                        )}
                                </ListboxButton>

                                {open && (
                                    <div className="fixed z-50 w-[--button-width]">
                                        <ListboxOptions
                                            static
                                            className="mt-1 max-h-60 overflow-auto rounded-md bg-white p-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none space-y-1">
                                            {options.map((option) => (
                                                <ListboxOption
                                                    key={option.value}
                                                    value={option.value}
                                                    className='relative cursor-default select-none'
                                                >
                                                    {({ focus }) => (
                                                        <span
                                                            className={classNames({ 'bg-qb-gray-40 ': focus }, 'relative flex items-center gap-2 py-1 px-4  rounded-md text-qb-black-100')}>
                                                            {option.name}
                                                        </span>
                                                    )}
                                                </ListboxOption>
                                            ))}
                                        </ListboxOptions>
                                    </div>
                                )}
                            </div>
                        )}
                    </Listbox>
                </div>
                to
                <input
                    id="searchTerm"
                    name="searchTerm"
                    type="text"
                    placeholder="enter keyword, e.g. Acquisition"
                    value={searchTerm}
                    onChange={(event) => setSearchTerm(event.target.value)}
                    className="flex-1 w-full rounded rounded-l-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
            </div>
        </>
    );
}
