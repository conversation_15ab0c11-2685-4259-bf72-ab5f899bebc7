import React, { SetStateAction } from 'react';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import ThresholdInput from './ThresholdInput';
import { AlertThreshold } from '../../../../../types/src/Alert';

export default function PriceMovementAlertForm({
    threshold,
    setThreshold
}: {
    threshold: AlertThreshold;
    setThreshold: React.Dispatch<SetStateAction<AlertThreshold>>;
}) {
    return (
        <>
            <div className='text-qb-gray-150 text-sm'>Movement threshold</div>
            <div className='flex items-center gap-2 text-qb-black-100'>
                <div className="inline-flex items-center border border-qb-gray-105 rounded-lg bg-white overflow-hidden">

                    <span className="px-3 whitespace-nowrap">When share price has moved</span>

                    <Popover as="span" className="relative py-1.5  bg-gray-100 border-l border-qb-gray-105">
                        <PopoverButton
                            as={'span'}
                            className=" px-3 py-1.5 inline font-semibold underline text-indigo-700 cursor-pointer bg-qb-gray-40 rounded-none">
                            {threshold?.threshold.toLocaleString()}%
                        </PopoverButton>
                        <PopoverPanel
                            anchor={{
                                to: 'bottom'
                            }}
                            className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                            {({ close }) => (
                                <ThresholdInput
                                    type="SHARE_PERCENT"
                                    close={close}
                                    threshold={threshold}
                                    setThreshold={setThreshold}
                                />
                            )}
                        </PopoverPanel>
                    </Popover>{' '}
                </div>
                <span>compared to price at open</span>
            </div>
        </>
    );
}
