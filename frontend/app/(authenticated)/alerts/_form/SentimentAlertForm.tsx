import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React from 'react';
import ThresholdInput from './ThresholdInput';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';
import classNames from 'classnames';

const options = [
    { value: 'LTE', label: 'less than' },
    { value: 'GTE', label: 'more than' }
];

export default function SentimentAlertForm({
    threshold,
    setThreshold
}: {
    threshold: {
        comparator: 'GTE' | 'LTE';
        threshold: number;
    };
    setThreshold: React.Dispatch<
        React.SetStateAction<{ comparator: 'GTE' | 'LTE'; threshold: number }>
    >;
}) {
    return (
        <>
            <span className="text-qb-gray-150 text-sm">Sentiment threhold</span>
            <div className="flex items-center gap-2">
                <div className="inline-flex items-center border border-qb-gray-105 rounded-lg bg-white overflow-hidden">
                    <span className="px-3 whitespace-nowrap text-qb-black-100">Sentiment is</span>
                    <Listbox
                        value={threshold?.comparator}
                        onChange={(val: 'GTE' | 'LTE') =>
                            setThreshold({
                                ...threshold,
                                comparator: val
                            })
                        }>
                        {({ open }) => (
                            <div className="relative border-l border-qb-gray-105">
                                <ListboxButton className="relative cursor-default rounded-r-md bg-qb-gray-40 py-1.5 px-3 text-left border-none focus:ring-0 outline-none w-fit">
                                    {options.find(
                                        (opt) => opt.value === threshold?.comparator
                                    )?.label || (
                                            <span className="text-gray-500">Select</span>
                                        )}
                                </ListboxButton>

                                {open && (
                                    <div className="fixed z-50 w-[--button-width]">
                                        <ListboxOptions
                                            static
                                            className="mt-1 max-h-60 overflow-auto rounded-md bg-white p-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none space-y-1">
                                            {options.map((option) => (
                                                <ListboxOption
                                                    key={option.value}
                                                    value={option.value}
                                                    className='relative cursor-default select-none'
                                                >
                                                    {({ focus }) => (
                                                        <span
                                                            className={classNames({ 'bg-qb-gray-40 ': focus }, 'relative flex items-center gap-2 py-1 px-4  rounded-md text-qb-black-100')}>
                                                            {option.label}
                                                        </span>
                                                    )}
                                                </ListboxOption>
                                            ))}
                                        </ListboxOptions>
                                    </div>
                                )}
                            </div>
                        )}
                    </Listbox>
                </div>
                <Popover as="span" className="relative">
                    <PopoverButton
                        as={'span'}
                        className="inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer border border-qb-gray-105 rounded-md bg-qb-gray-40 px-3 py-2 rounded-md">
                        {threshold?.threshold.toLocaleString()}
                    </PopoverButton>
                    <PopoverPanel
                        anchor={{
                            to: 'bottom'
                        }}
                        className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                        {({ close }) => (
                            <ThresholdInput
                                type="SENTIMENT"
                                close={close}
                                threshold={threshold}
                                setThreshold={setThreshold}
                                min={-1}
                                max={1}
                            />
                        )}
                    </PopoverPanel>
                </Popover>
            </div>
        </>
    );
}
