import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React from 'react';
import ThresholdInput from './ThresholdInput';
import { ActivityAlertInterval } from '@quarterback/types';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';
import classNames from 'classnames';

const intervalOptions: { value: ActivityAlertInterval; label: string }[] = [
    { value: 'DAY', label: 'day' },
    { value: 'WEEK', label: 'week' },
    { value: 'MONTH', label: 'month' }
];

export default function ActivityAlertForm({
    threshold,
    setThreshold,
    interval,
    setInterval
}: {
    threshold: {
        comparator: 'GTE' | 'LTE';
        threshold: number;
    };
    setThreshold: React.Dispatch<
        React.SetStateAction<{ comparator: 'GTE' | 'LTE'; threshold: number }>
    >;
    interval: ActivityAlertInterval;
    setInterval: React.Dispatch<React.SetStateAction<ActivityAlertInterval>>;
}) {
    return (
        <>
            <span className="text-qb-gray-150 text-sm">Keywords</span>
            <div className="flex items-center gap-2 text-qb-black-100 text-qb-black-100">
                <div className="inline-flex items-center border border-qb-gray-105 rounded-lg bg-white overflow-hidden">
                    <span className="px-3 whitespace-nowrap">
                        Total activity count has changed by
                    </span>
                    <Popover
                        as="span"
                        className="relative py-1.5  bg-gray-100 border-l border-gray-100">
                        <PopoverButton
                            as={'span'}
                            className=" px-3 py-1.5 inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer bg-qb-gray-40">
                            {threshold?.threshold.toLocaleString()}%
                        </PopoverButton>
                        <PopoverPanel
                            anchor={{
                                to: 'bottom'
                            }}
                            className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                            {({ close }) => (
                                <ThresholdInput
                                    type="ACTIVITY"
                                    close={close}
                                    threshold={threshold}
                                    setThreshold={setThreshold}
                                    min={0.01}
                                    max={100}
                                    step={0.01}
                                />
                            )}
                        </PopoverPanel>
                    </Popover>{' '}
                </div>
                compared to previous{' '}
                <Listbox value={interval} onChange={(val) => setInterval(val)}>
                    {({ open }) => (
                        <div className="relative">
                            <ListboxButton className="relative cursor-default rounded-md bg-qb-gray-40 py-1.5 px-3 text-left border border-qb-gray-105 focus:ring-0 outline-none w-fit">
                                {intervalOptions.find((opt) => opt.value === interval!)
                                    ?.label || (
                                        <span className="text-gray-500">Select</span>
                                    )}
                            </ListboxButton>

                            {open && (
                                <div className="fixed z-50 w-[--button-width]">
                                    <ListboxOptions
                                        static
                                        className="mt-1 max-h-60 overflow-auto rounded-md bg-white p-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none space-y-1">
                                        {intervalOptions.map((option) => (
                                            <ListboxOption
                                                key={option.value}
                                                value={option.value}
                                                className='relative cursor-default select-none'
                                            >
                                                {({ focus }) => (
                                                    <span
                                                        className={classNames({ 'bg-qb-gray-40 ': focus }, 'relative flex items-center gap-2 py-1 px-4  rounded-md text-qb-black-100')}>
                                                        {option.label}
                                                    </span>
                                                )}

                                            </ListboxOption>
                                        ))}
                                    </ListboxOptions>
                                </div>
                            )}
                        </div>
                    )}
                </Listbox >
            </div >
        </>
    );
}
