import useAlertRulesMutation from '@/api/hooks/mutations/useAlertRulesMutation';
import AlertNotificationConfig from '@/app/(authenticated)/alerts/_form/AlertNotificationConfig';
import AlertTypePicker from '@/app/(authenticated)/alerts/_form/AlertTypePicker';
import PlaceholderAlertForm from '@/app/(authenticated)/alerts/_form/PlaceholderAlertForm';
import PriceMovementAlertForm from '@/app/(authenticated)/alerts/_form/PriceMovementAlertForm';
import SearchAlertForm from '@/app/(authenticated)/alerts/_form/SearchAlertForm';
import { useOrganisation } from '@/components/OrganisationProvider';
import { Dialog, DialogPanel, DialogTitle, Transition } from '@headlessui/react';
import { ActivityAlertInterval, AlertField, AlertThreshold } from '@quarterback/types';
import React, { useMemo, useState } from 'react';
import ActivityAlertForm from './ActivityAlertForm';
import SentimentAlertForm from './SentimentAlertForm';
import { BellAlertIcon, XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';

const INITIAL_SENTIMENT_THRESHOLD: AlertThreshold = {
    comparator: 'GTE',
    threshold: 0.75
};
const INITIAL_THRESHOLD: AlertThreshold = {
    comparator: 'GTE',
    threshold: 10
};

const INITIAL_ACTIVITY_THRESHOLD: AlertThreshold = {
    comparator: 'GTE',
    threshold: 10
};
const INITIAL_BROAD_SEARCH_THRESHOLD: AlertThreshold = {
    comparator: 'GTE',
    threshold: 0.48
};

export default function AlertForm({
    setOpen,
    open
}: {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    const organisation = useOrganisation();

    const { create: createAlerts } = useAlertRulesMutation(
        organisation?.selected?.organisation,
        organisation?.selected?.entity
    );

    const [type, setType] = useState<AlertField | undefined>(undefined);
    const [emails, setEmails] = useState<Array<string>>([]);
    const [interval, setInterval] = useState<ActivityAlertInterval>('DAY');

    const [threshold, setThreshold] = useState<AlertThreshold>(INITIAL_THRESHOLD);

    const [sentimentThreshold, setSentimentThreshold] = useState<AlertThreshold>(
        INITIAL_SENTIMENT_THRESHOLD
    );

    const [activityThreshold, setActivityThreshold] = useState<AlertThreshold>(
        INITIAL_ACTIVITY_THRESHOLD
    );

    const [broadSearchThreshold, setBroadSearchThreshold] = useState<AlertThreshold>(
        INITIAL_BROAD_SEARCH_THRESHOLD
    );

    const [searchTerm, setSearchTerm] = useState<string>('');

    const classes = [
        'w-full transition ease-in-out data-[closed]:opacity-0',
        'data-[leave]:duration-300 data-[leave]:data-[closed]:-translate-x-full',
        'data-[enter]:duration-100 data-[enter]:data-[closed]:translate-x-full'
    ];

    const resetForm = () => {
        setType(undefined);
        setEmails([]);
        setThreshold(INITIAL_THRESHOLD);
        setSentimentThreshold(INITIAL_SENTIMENT_THRESHOLD);
        setActivityThreshold(INITIAL_ACTIVITY_THRESHOLD);
        setBroadSearchThreshold(INITIAL_BROAD_SEARCH_THRESHOLD);
    };

    const form = useMemo(() => {
        switch (type) {
            case 'SEARCH':
                return (
                    <SearchAlertForm
                        threshold={broadSearchThreshold}
                        setThreshold={setBroadSearchThreshold}
                        searchTerm={searchTerm}
                        setSearchTerm={setSearchTerm}
                    />
                );
            case 'SHARE_PERCENT':
                return (
                    <PriceMovementAlertForm
                        threshold={threshold}
                        setThreshold={setThreshold}
                    />
                );
            case 'SENTIMENT':
                return (
                    <SentimentAlertForm
                        threshold={sentimentThreshold}
                        setThreshold={setSentimentThreshold}
                    />
                );
            case 'ACTIVITY':
                return (
                    <ActivityAlertForm
                        threshold={activityThreshold}
                        setThreshold={setActivityThreshold}
                        interval={interval}
                        setInterval={setInterval}
                    />
                );
            default:
                return <PlaceholderAlertForm />;
        }
    }, [
        type,
        broadSearchThreshold,
        searchTerm,
        threshold,
        sentimentThreshold,
        activityThreshold,
        interval
    ]);

    const title = useMemo(() => {
        switch (type) {
            case 'SHARE_PERCENT':
                return {
                    title: 'Price movement alert',
                    description:
                        'Alerts you to a change in your share price that may require attention.'
                };
            case 'SENTIMENT':
                return {
                    title: 'Sentiment alert',
                    description:
                        'Alerts you to any new activities that fall within certain sentiment parameters.'
                };
            case 'ACTIVITY':
                return {
                    title: 'Activity level alert',
                    description:
                        'Alerts you when the activity count changes by certain percentage'
                };
            case 'SEARCH':
                return {
                    title: 'Broad Search Alert',
                    description:
                        'Alerts you to conversations about broad topics or themes happening right now.'
                };
            default:
                return undefined;
        }
    }, [type]);

    const valid = useMemo(() => {
        if (!type) return false;
        if (!emails.length) return false;

        switch (type) {
            case 'SHARE_PERCENT':
                return true;
            case 'SENTIMENT':
                return true;
            case 'ACTIVITY':
                return true;
            case 'SEARCH':
                return searchTerm.length > 0;
            default:
                return false;
        }
    }, [type, emails.length, searchTerm.length]);

    // TODO: Probs move this up a level or smth
    async function handleSubmit() {
        function getThreshold(type: AlertField): AlertThreshold {
            if (type === 'SENTIMENT')
                return {
                    comparator: sentimentThreshold?.comparator,
                    threshold: sentimentThreshold?.threshold
                };

            if (type === 'ACTIVITY')
                return {
                    comparator: 'GTE',
                    threshold: activityThreshold?.threshold
                };

            if (type === 'SEARCH')
                return {
                    comparator: 'GTE',
                    threshold: broadSearchThreshold?.threshold
                };

            return {
                comparator: 'GTE',
                threshold: threshold?.threshold
            };
        }

        const thresholdData = getThreshold(type as AlertField);

        await createAlerts([
            {
                threshold: thresholdData?.threshold,
                emails,
                field: type!,
                comparator: thresholdData?.comparator,
                interval: type === 'ACTIVITY' ? interval : null,
                searchTerm: type === 'SEARCH' ? searchTerm : null
            }
        ]);

        resetForm();
        setOpen(false);
    }

    const closeDialog = () => {
        resetForm();
        setOpen(false);
    };
    return (
        <Dialog open={open} onClose={() => { }} className="relative z-50">
            <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

            <div className="fixed inset-0 flex items-center justify-center p-4">
                <DialogPanel className="w-full max-w-3xl rounded-xl bg-white shadow-xl">
                    <DialogTitle className="px-6 py-3 text-base text-gray-700 font-medium flex justify-between items-center border-b border-gray-200">
                        <span className=" flex items-center gap-2">
                            <BellAlertIcon className="h-4 w-4 "></BellAlertIcon>New Notification
                        </span>
                        <button className="" onClick={closeDialog}>
                            <XMarkIcon className="h-5 w-5" />
                        </button>
                    </DialogTitle>

                    <div className="px-6 py-3 text-qb-black-100">
                        <div className="py-2">
                            <span className="text-qb-gray-150 text-sm mb-2">
                                Notification Type
                            </span>
                            <AlertTypePicker setType={setType} type={type} />
                        </div>
                        {!!type && (
                            <>
                                <div className="py-2">
                                    <span className="text-qb-gray-150 text-sm mb-2">
                                        {' '}
                                        Description
                                    </span>
                                    <p className="mt-1">{title?.description}</p>
                                </div>
                                <div className="py-2 flex flex-col gap-2">
                                    {form}
                                    <AlertNotificationConfig
                                        emails={emails}
                                        setEmails={setEmails}
                                    />
                                </div>
                            </>
                        )}
                    </div>

                    <div className="bg-qb-gray-5 border-t border-gray-200 p-3 px-6 rounded-b-xl flex justify-end gap-2">
                        <Button
                            variant="secondary"
                            size="sm"
                            onClick={closeDialog}
                            className="flex items-center gap-2">
                            <XMarkIcon className="h-4 w-4" />
                            Cancel
                        </Button>
                        <Button
                            size="sm"
                            onClick={handleSubmit}
                            disabled={!valid}
                            className="disabled:bg-gray-400">
                            Create Notification
                        </Button>
                    </div>
                </DialogPanel>
            </div>
        </Dialog>
    );
}
