import { ArrowUpIcon } from '@heroicons/react/16/solid';
import {
    ExclamationCircleIcon,
    FaceFrownIcon,
    MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON>Field } from '@quarterback/types';
import classNames from 'classnames';
import { useMemo } from 'react';

export default function AlertFieldIcon({ field }: { field: AlertField }) {
    const color = useMemo(() => {
        switch (field) {
            case 'SHARE_PERCENT':
                return 'bg-pink-500';
            case 'SENTIMENT':
                return 'bg-blue-500';
            case 'SEARCH':
                return 'bg-purple-500';
            case 'ACTIVITY':
                return 'bg-green-500';
        }
    }, [field]);

    const Icon = useMemo(() => {
        switch (field) {
            case 'SHARE_PERCENT':
                return ExclamationCircleIcon;
            case 'SENTIMENT':
                return FaceFrownIcon;
            case 'SEARCH':
                return MagnifyingGlassIcon;
            case 'ACTIVITY':
                return ArrowUpIcon;
        }
    }, [field]);

    const containerSize = `h-4 w-4 rounded-md`;
    const iconSize = `h-3 w-3`;
    return (
        <span
            className={classNames(
                color,
                containerSize,
                'inline-flex items-center justify-center '
            )
            }>
            <Icon aria-hidden="true" className={classNames(iconSize, "text-white")} />
        </span >
    );
}
