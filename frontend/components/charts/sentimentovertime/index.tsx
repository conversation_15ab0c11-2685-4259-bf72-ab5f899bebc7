import { useMemo } from 'react';
import { formatWithTimeZone } from '@/util/date';
import { Activity } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { scaleLinear, scaleTime } from '@visx/scale';
import { extent } from '@visx/vendor/d3-array';
import { Group } from '@visx/group';
import { LinePath } from '@visx/shape';
import { curveMonotoneX } from '@visx/curve';
import { AxisBottom, AxisLeft, Orientation } from '@visx/axis';
import { GridRows } from '@visx/grid';
import { ParentSize } from '@visx/responsive';
import { sentimentScore } from '@/util/sentiment';
import hasField from '@/util/hasField';

interface SentimentOverTimeChartProps {
    activities: Array<Activity>;
    width: number;
    height: number;
}

const SentimentOverTimeChart = function SentimentOverTimeChart({
    activities,
    width,
    height
}: SentimentOverTimeChartProps) {
    const data = useMemo(() => {
        const dailyData = Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(a[0]).valueOf() - new Date(b[0]).valueOf())
            .map(([day, dayActivities]) => {
                // Filter activities that have sentiment data
                const activitiesWithSentiment = dayActivities.filter(
                    hasField('sentiment')
                );

                if (activitiesWithSentiment.length === 0) {
                    return {
                        date: new Date(day),
                        averageSentiment: 0
                    };
                }

                // Calculate average sentiment for the day
                const totalSentiment = activitiesWithSentiment.reduce(
                    (sum, activity) => sum + sentimentScore(activity.sentiment),
                    0
                );
                const averageSentiment = totalSentiment / activitiesWithSentiment.length;

                return {
                    date: new Date(day),
                    averageSentiment
                };
            });

        return dailyData;
    }, [activities]);

    const margin = { top: 20, right: 18, bottom: 25, left: 15 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const xScale = useMemo(() => {
        const dateExtent = extent(data, (d) => d.date) as [Date, Date];
        return scaleTime({
            domain: dateExtent,
            range: [0, innerWidth]
        });
    }, [data, innerWidth]);

    const yScale = useMemo(() => {
        return scaleLinear({
            domain: [-1, 1], // Sentiment range from -1 to 1
            range: [innerHeight, 0]
        });
    }, [innerHeight]);

    // Format functions for axes
    const xTickFormat = (value: Date | { valueOf(): number }) => {
        if (value instanceof Date) {
            return formatWithTimeZone(value, 'd MMM');
        } else {
            return formatWithTimeZone(value.valueOf(), 'd MMM');
        }
    };

    const yTickFormat = (value: number | { valueOf(): number }) => {
        if (typeof value === 'number') {
            return value.toFixed(1);
        } else {
            return value.valueOf().toFixed(1);
        }
    };

    const AXIS_COLOR = '#94a3b8';
    const LINE_COLOR = '#8B5CF6'; // Purple color to match the design

    // Handle empty data case
    if (!data || data.length === 0) {
        return (
            <div
                style={{
                    width: '100%',
                    height,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#6b7280',
                    fontSize: '14px'
                }}>
                No data available
            </div>
        );
    }

    return (
        <div style={{ width: '100%' }}>
            <svg width={width} height={height}>
                <Group left={margin.left} top={margin.top}>
                    {/* Grid lines */}
                    <GridRows
                        scale={yScale}
                        width={innerWidth}
                        stroke="#e2e8f0"
                        strokeWidth={1}
                        strokeOpacity={0.6}
                        numTicks={5}
                    />

                    {/* Render sentiment line */}
                    <LinePath
                        data={data}
                        x={(d) => xScale(d.date) ?? 0}
                        y={(d) => yScale(d.averageSentiment) ?? 0}
                        stroke={LINE_COLOR}
                        strokeWidth={2}
                        fill="none"
                        curve={curveMonotoneX}
                    />

                    {/* Y-axis on the right */}
                    <AxisLeft
                        scale={yScale}
                        orientation={Orientation.left}
                        tickFormat={yTickFormat}
                        left={innerWidth + 18}
                        tickStroke={AXIS_COLOR}
                        stroke={AXIS_COLOR}
                        tickLabelProps={{ fill: AXIS_COLOR, fontSize: 12 }}
                        numTicks={5}
                    />

                    {/* X-axis at the bottom */}
                    <AxisBottom
                        scale={xScale}
                        orientation={Orientation.bottom}
                        tickFormat={xTickFormat}
                        top={innerHeight}
                        tickStroke={AXIS_COLOR}
                        stroke={AXIS_COLOR}
                        numTicks={6}
                        tickLabelProps={{ fill: AXIS_COLOR, fontSize: 12 }}
                    />
                </Group>
            </svg>
        </div>
    );
};

interface Props {
    activities: Array<Activity>;
    height?: number;
}

export default function SentimentOverTime({ activities, height = 300 }: Props) {
    return (
        <div style={{ width: '100%', height }}>
            <ParentSize>
                {({ width }) => (
                    <SentimentOverTimeChart
                        activities={activities}
                        width={width}
                        height={height}
                    />
                )}
            </ParentSize>
        </div>
    );
}
