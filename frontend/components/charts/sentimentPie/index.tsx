import React, { useMemo } from 'react';
import { Activity } from '@quarterback/types';
import { ParentSize } from '@visx/responsive';
import { Group } from '@visx/group';
import Pie, { PieArcDatum } from '@visx/shape/lib/shapes/Pie';
import { useSentimentCount, DiscreteSentiment } from '@/util/sentiment';

interface SentimentData {
    label: string;
    count: number;
    color: string;
    sentiment: DiscreteSentiment;
}

const SENTIMENT_COLORS = {
    [DiscreteSentiment.NEGATIVE]: '#f87171', // red-300
    [DiscreteSentiment.LACKING]: '#facc15', // yellow-300
    [DiscreteSentiment.NEUTRAL]: '#e2e8f0', // slate-200
    [DiscreteSentiment.POSITIVE]: '#4ade80' // green-300
};

const SENTIMENT_LABELS = {
    [DiscreteSentiment.NEGATIVE]: 'Negative',
    [DiscreteSentiment.LACKING]: 'Lacking',
    [DiscreteSentiment.NEUTRAL]: 'Neutral',
    [DiscreteSentiment.POSITIVE]: 'Positive'
};

export const SentimentPie = function SentimentPie({
    data,
    width: parentWidth = 200,
    height: parentHeight = 200
}: {
    data: Array<SentimentData>;
    width: number;
    height: number;
}) {
    const width = parentWidth;
    const height = parentHeight;

    const radius = Math.min(width, height) / 2;
    const centerY = height / 2;
    const centerX = width / 2;

    const getValue = (d: SentimentData) => d.count;
    const getColor = (arc: PieArcDatum<SentimentData>) => arc.data.color;

    const filteredData = data.filter((d) => d.count > 0);

    if (filteredData.length === 0) {
        return (
            <svg width={width} height={height}>
                <Group top={centerY} left={centerX}>
                    <circle r={radius} fill="#f3f4f6" />
                    <text textAnchor="middle" dy="0.33em" fontSize={14} fill="#6b7280">
                        No data
                    </text>
                </Group>
            </svg>
        );
    }

    return (
        <svg width={width} height={height}>
            <Group top={centerY} left={centerX}>
                <Pie
                    data={filteredData}
                    pieValue={getValue}
                    outerRadius={radius}
                    innerRadius={0}
                    cornerRadius={2}
                    padAngle={0.01}>
                    {(pie) => (
                        <>
                            {pie.arcs.map((arc, index) => (
                                <path
                                    key={`arc-${index}`}
                                    d={pie.path(arc) || ''}
                                    fill={getColor(arc)}
                                />
                            ))}
                        </>
                    )}
                </Pie>
            </Group>
        </svg>
    );
};

interface Props {
    activities: Array<Activity>;
}

export default function SentimentDisclosure({ activities }: Props) {
    const [negativeCount, lackingCount, neutralCount, positiveCount] =
        useSentimentCount(activities);

    const data: Array<SentimentData> = useMemo(
        () => [
            {
                label: SENTIMENT_LABELS[DiscreteSentiment.NEGATIVE],
                count: negativeCount,
                color: SENTIMENT_COLORS[DiscreteSentiment.NEGATIVE],
                sentiment: DiscreteSentiment.NEGATIVE
            },
            {
                label: SENTIMENT_LABELS[DiscreteSentiment.LACKING],
                count: lackingCount,
                color: SENTIMENT_COLORS[DiscreteSentiment.LACKING],
                sentiment: DiscreteSentiment.LACKING
            },
            {
                label: SENTIMENT_LABELS[DiscreteSentiment.NEUTRAL],
                count: neutralCount,
                color: SENTIMENT_COLORS[DiscreteSentiment.NEUTRAL],
                sentiment: DiscreteSentiment.NEUTRAL
            },
            {
                label: SENTIMENT_LABELS[DiscreteSentiment.POSITIVE],
                count: positiveCount,
                color: SENTIMENT_COLORS[DiscreteSentiment.POSITIVE],
                sentiment: DiscreteSentiment.POSITIVE
            }
        ],
        [negativeCount, lackingCount, neutralCount, positiveCount]
    );

    return (
        <>
            <div className="h-56 p-4 relative">
                <ParentSize>
                    {({ width, height }) => (
                        <SentimentPie width={width} height={height} data={data} />
                    )}
                </ParentSize>
            </div>
            <div className="flex flex-wrap gap-y-2 gap-x-4">
                {data
                    .filter((item) => item.count > 0)
                    .map((item) => (
                        <div
                            key={item.label}
                            className="text-sm flex items-center gap-x-2">
                            <div
                                className="w-3 h-3 rounded-full flex-shrink-0"
                                style={{ backgroundColor: item.color }}
                            />
                            <span className="truncate">{item.label}</span>
                            {/* <span className="text-gray-500 ml-auto">{item.count}</span> */}
                        </div>
                    ))}
            </div>
        </>
    );
}
