import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import activityFormat from '@/util/activityFormat';
import { formatWithTimeZone } from '@/util/date';
import { CurrencyDollarIcon, HashtagIcon } from '@heroicons/react/24/outline';
import { Activity, ListedEntity, TwitterProfileSnapshot } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { localPoint } from '@visx/event';
import { Group } from '@visx/group';
import { AnyScaleBand } from '@visx/shape/lib/types';
import React, { useCallback, useMemo } from 'react';
import { FollowerDataPoint } from './FollowersChart';
import source from '@/util/source';

export interface SourceData {
    name: string;
    count: number;
}

export interface SentimentDataPoint {
    date: string;
    averageSentiment: number;
    countByType?: Record<string, number>;
}

export interface TooltipData {
    day: string;
    quote: TimeSeriesQuote;
    broadcasts: number;
    chatter: number;
    media: number;
    announcements: Array<string>;
    twitter: number | undefined;
    linkedin: number | undefined;
    sources?: Array<SourceData>;
    avgSentiment: number;
    sentiments: Record<string, number>;
}

export function FocusedColumn({
    height,
    xScale,
    focused
}: {
    height: number;
    focused: string | undefined;
    xScale: AnyScaleBand;
}) {
    const boxWidth = xScale.bandwidth();

    if (focused) {
        return (
            <Group top={32} left={64}>
                <rect
                    x={xScale(focused)}
                    y={0}
                    width={boxWidth}
                    height={height}
                    rx={4}
                    fill="#000"
                    opacity={0.04}
                />
            </Group>
        );
    }
}

export function TooltipTargets({
    showTooltip,
    hideTooltip,
    onClick,
    entity,
    quotes,
    activities,
    followers,
    height,
    xScale,
    filterBy,
    sentiment
}: {
    hideTooltip: () => void;
    showTooltip: (args: {
        tooltipData: TooltipData;
        tooltipLeft: number;
        tooltipTop: number;
    }) => void;
    onClick?: (datetime: string) => void;
    entity: ListedEntity | undefined;
    quotes: Array<TimeSeriesQuote>;
    activities: Array<Activity>;
    followers: Array<FollowerDataPoint>;
    height: number;
    xScale: AnyScaleBand;
    filterBy: string;
    sentiment: Array<SentimentDataPoint>;
} & React.PropsWithChildren) {
    const boxWidth = xScale.bandwidth();

    const activitiesByDay = useMemo((): Record<string, Array<Activity>> => {
        return groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'));
    }, [activities]);

    const followersByDay = useMemo(() => {
        return groupBy(followers, (it) => formatWithTimeZone(it.datetime, 'yyyy-MM-dd'));
    }, [followers]);

    const avgSentimentByDay = useMemo(() => {
        return groupBy(sentiment, (it) => formatWithTimeZone(it.date, 'yyyy-MM-dd'));
    }, [sentiment]);

    const handleTooltip = useCallback(
        function handleTooltip(
            event: React.TouchEvent<SVGRectElement> | React.MouseEvent<SVGRectElement>,
            quote: TimeSeriesQuote
        ) {
            const { y } = localPoint(event) || { x: 0, y: 0 };
            const acitivitiesByType = groupBy(
                activitiesByDay?.[quote.datetime] ?? [],
                (it) => activityFormat(it)
            );
            const activitiesBySourceList = groupBy(
                activitiesByDay?.[quote.datetime] ?? [],
                (it) => source(it)
            );

            const activitiesBySource = Object.keys(activitiesBySourceList).map((a) => ({
                name: a,
                count: activitiesBySourceList[a].length
            }));

            const followersCount = followersByDay[quote.datetime] ?? [];
            showTooltip({
                tooltipData: {
                    quote,
                    day: quote.datetime,
                    broadcasts: acitivitiesByType['broadcast']?.length ?? 0,
                    chatter: acitivitiesByType['chatter']?.length ?? 0,
                    media: acitivitiesByType['media']?.length ?? 0,
                    announcements:
                        acitivitiesByType['announcement']
                            ?.filter((it) => it.type === 'asx-announcement')
                            .map((it) => it.title) ?? [],

                    twitter: followersCount?.[0]?.twitter ?? 0,
                    linkedin: followersCount?.[0]?.linkedin ?? 0,
                    sources: activitiesBySource,
                    avgSentiment:
                        avgSentimentByDay[quote.datetime]?.[0]?.averageSentiment,
                    sentiments: avgSentimentByDay[quote.datetime]?.[0]?.countByType || {}
                },
                tooltipLeft: xScale(quote.datetime)!,
                tooltipTop: y
            });
        },
        [activitiesByDay, entity, followersByDay, showTooltip, xScale]
    );

    return (
        <>
            <Group top={32} left={64}>
                {quotes.map((quote) => (
                    <rect
                        key={quote.datetime}
                        x={xScale(quote.datetime)}
                        y={0}
                        width={boxWidth}
                        height={height}
                        rx={4}
                        fill="#000"
                        fillOpacity={0}
                        opacity={0}
                        style={{ cursor: 'pointer' }}
                        onMouseEnter={(e) => handleTooltip(e, quote)}
                        onMouseMove={(e) => handleTooltip(e, quote)}
                        onMouseLeave={hideTooltip}
                        onClick={() => onClick?.(quote.datetime)}
                    />
                ))}
            </Group>
        </>
    );
}

export default function Tooltip({
    tooltipData,
    filterBy,
    overlay
}: {
    tooltipData: TooltipData;
    filterBy: string;
    overlay?: string;
}) {
    return (
        <div className="flex flex-col gap-y-1 text-sm text-gray-900">
            <span className="font-bold">
                {formatWithTimeZone(new Date(tooltipData.day), 'MMM d')}
            </span>
            <div className="border-t my-1 border-gray-200" />

            <div className="flex gap-x-3 items-center justify-between">
                <span className="text-gray-500 flex items-center gap-1">
                    <CurrencyDollarIcon className="h-4 w-4" />
                    Open
                </span>
                <span className="text-right font-medium font-medium">
                    ${tooltipData.quote.open.toFixed(4)}
                </span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="text-gray-500 flex items-center gap-1">
                    <CurrencyDollarIcon className="h-4 w-4" />
                    Close
                </span>
                <span className="text-right font-medium">
                    ${tooltipData.quote.close.toFixed(4)}
                </span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="text-gray-500 flex items-center gap-1">
                    <HashtagIcon className="h-4 w-4" />
                    Trade volume
                </span>
                <span className="text-right font-medium">
                    {tooltipData.quote.volume.toLocaleString()}
                </span>
            </div>
            <div className="border-t my-1 border-gray-200" />
            {filterBy === 'format' && (
                <>
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold bg-lime-100 text-lime-700 px-2 py-1 text-xs ring-1 ring-inset ring-lime-700 rounded-md">
                            Broadcasts
                        </span>
                        <span className="text-right font-medium">
                            {tooltipData.broadcasts}
                        </span>
                    </div>
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold bg-yellow-100 text-yellow-700 px-2 py-1 text-xs ring-1 ring-inset ring-yellow-700 rounded-md">
                            Chatter
                        </span>
                        <span className="text-right font-medium">
                            {tooltipData.chatter}
                        </span>
                    </div>
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold bg-red-100 text-red-700 px-2 py-1 text-xs ring-1 ring-inset ring-red-700 rounded-md">
                            Media
                        </span>
                        <span className="text-right font-medium">
                            {tooltipData.media}
                        </span>
                    </div>
                </>
            )}
            {filterBy === 'source' &&
                tooltipData?.sources?.length &&
                tooltipData.sources.slice(0, 5).map((item) => (
                    <>
                        <div className="flex gap-x-3 items-center justify-between">
                            <span className="text-gray-500">{item.name}</span>
                            <span className="text-right font-medium">{item.count}</span>
                        </div>
                    </>
                ))}
            {overlay === 'followers' && (
                <>
                    <div className="border-t my-1 border-gray-200" />
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold">Twitter</span>
                        <span className="text-right font-medium">
                            {tooltipData.twitter}
                        </span>
                    </div>
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold">Linkedin</span>
                        <span className="text-right font-medium">
                            {tooltipData.linkedin}
                        </span>
                    </div>
                </>
            )}

            {tooltipData.avgSentiment !== undefined && filterBy === 'format' && (
                <>
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold">Avg. sentiment</span>
                        <span className="text-right font-medium">
                            {tooltipData.avgSentiment.toFixed(4)}
                        </span>
                    </div>
                </>
            )}
            {tooltipData.sentiments !== undefined && filterBy === 'sentiment' && (
                <>
                    <div className="border-t my-1 border-gray-200" />
                    {Object.keys(tooltipData.sentiments).map((it) => (
                        <>
                            <div className="flex gap-x-3 items-center justify-between">
                                <span className="font-semibold">{it}</span>
                                <span className="text-right font-medium">
                                    {tooltipData.sentiments[it].toFixed(2)}
                                </span>
                            </div>
                        </>
                    ))}
                </>
            )}
            {tooltipData.announcements.length !== 0 && filterBy === 'format' && (
                <>
                    <div className="border-t my-1 border-gray-200" />
                    <div>
                        <span className="font-semibold bg-blue-100 text-blue-700 px-2 py-1 text-xs ring-1 ring-inset ring-blue-700 rounded-md">
                            Announcements
                        </span>
                    </div>
                    {tooltipData.announcements.map((announcement, index) => (
                        <span key={index}>{announcement}</span>
                    ))}
                </>
            )}
        </div>
    );
}
