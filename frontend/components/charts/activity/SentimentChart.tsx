import { Group } from '@visx/group';
import { scaleLinear } from '@visx/scale';
import { LinePath } from '@visx/shape';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { useMemo } from 'react';

export type FollowerDataPoint = {
    datetime: string;
    twitter: number;
    linkedin: number;
    total: number;
};

import { SentimentDataPoint } from './Tooltip';

export default function SentimentChart({
    xScale,
    height,
    top,
    sentiment
}: {
    xScale: AnyScaleBand;
    height: number;
    top: number;
    sentiment: Array<SentimentDataPoint>;
}) {
    const yScale = useMemo(() => {
        return scaleLinear({
            domain: [-1, 1], // Sentiment range from -1 to 1
            range: [height, 0]
        });
    }, [height]);

    return (
        <Group left={64 + xScale.bandwidth() / 2} top={top}>
            <LinePath
                stroke="#7620f0"
                strokeWidth={1}
                data={sentiment}
                x={(d) => xScale(d.date) ?? 0}
                y={(d) => yScale(d.averageSentiment) ?? 0}
            />
        </Group>
    );
}
