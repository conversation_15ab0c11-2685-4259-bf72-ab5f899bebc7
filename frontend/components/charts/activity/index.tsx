'use client';

import React, { useMemo } from 'react';
import { scaleBand } from '@visx/scale';
import { useTooltip, useTooltipInPortal } from '@visx/tooltip';
import VolumeChart from './VolumeChart';
import ActivityMarkers from './ActivityMarkers';
import PriceChart from './PriceChart';
import Axes from './Axes';
import AbnormalReturnsChart from './AbnormalReturnsChart';
import TradeVolumeChart from './TradeVolumeChart';
import { Activity, Followers, ListedEntity } from '@quarterback/types';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import Tooltip, {
    FocusedColumn,
    TooltipData,
    TooltipTargets
} from '@/components/charts/activity/Tooltip';
import FollowersChart from './FollowersChart';
import Sentiment<PERSON>hart from './SentimentChart';
import { groupBy } from '@quarterback/util';
import { formatWithTimeZone } from '@/util/date';
import hasField from '@/util/hasField';
import { sentimentScore } from '@/util/sentiment';

const DEFAULT_Y_AXIS_SCALE_PADDING = 0.8;

type Props = {
    entity: ListedEntity | undefined;
    timeSeries: Array<TimeSeriesQuote>;
    activities: Array<Activity>;
    followers: Followers;
    onClick?: (datetime: string) => void;
    width: number;
    height: number;
    style?: {
        price?: {
            yScalePadding?: number;
        };
    };
    filterBy: string;
    overlay?: string;
    timeRange?: string;
};

export default function ActivityChart({
    width: parentWidth,
    height: parentHeight,
    style,
    onClick,
    entity,
    timeSeries,
    activities,
    followers,
    filterBy,
    overlay,
    timeRange
}: Props) {
    const {
        tooltipOpen,
        tooltipLeft,
        tooltipTop,
        tooltipData,
        hideTooltip,
        showTooltip
    } = useTooltip<TooltipData>();
    const { containerRef, TooltipInPortal } = useTooltipInPortal({
        scroll: true
    });

    const width = Math.max(parentWidth, 100);
    const height = Math.max((parentHeight ?? 100) - 22 - 16 * 2, 100);
    const isWeek = !!timeRange && ['1D', '1W'].includes(timeRange);

    const followersByDayArr = useMemo(() => {
        const combinedFollowers = [
            ...(followers?.twitter?.map((item) => ({
                datetime: formatWithTimeZone(item.at, 'yyyy-MM-dd'),
                type: 'twitter',
                followers: item.followers
            })) || []),
            ...(followers?.linkedIn?.map((item) => ({
                datetime: formatWithTimeZone(item.at, 'yyyy-MM-dd'),
                type: 'linkedin',
                followers: item.followers
            })) || [])
        ];

        const dateMap = combinedFollowers.reduce(
            (acc, { datetime, type, followers }) => {
                if (!acc[datetime]) {
                    acc[datetime] = {
                        datetime: datetime,
                        twitter: 0,
                        linkedin: 0,
                        total: 0
                    };
                }
                acc[datetime][type as 'twitter' | 'linkedin'] = followers;
                acc[datetime].total += followers;
                return acc;
            },
            {} as Record<
                string,
                {
                    datetime: string;
                    twitter: number;
                    linkedin: number;
                    total: number;
                }
            >
        );

        return Object.values(dateMap).sort(
            (a, b) => new Date(a.datetime).valueOf() - new Date(b.datetime).valueOf()
        );
    }, [followers]);

    const x = (quote: TimeSeriesQuote) => quote.datetime;

    const sentimentData = useMemo(() => {
        return Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(a[0]).valueOf() - new Date(b[0]).valueOf())
            .map(([day, dayActivities]) => {
                // Filter activities that have sentiment data
                const activitiesWithSentiment = dayActivities.filter(
                    hasField('sentiment')
                );
                if (!activitiesWithSentiment.length)
                    return { date: day, averageSentiment: 0, total: 0 };

                // Calculate average sentiment for the day
                const totalSentiment = activitiesWithSentiment.reduce(
                    (sum, activity) => sum + sentimentScore(activity.sentiment),
                    0
                );
                const sentimentScores = activitiesWithSentiment.map((a) =>
                    sentimentScore(a.sentiment)
                );
                const averageSentiment = totalSentiment / activitiesWithSentiment.length;

                return {
                    date: day,
                    averageSentiment,
                    total: activitiesWithSentiment.length,
                    countByType: {
                        NEGATIVE: sentimentScores.filter((s) => s < -0.2).length,
                        LACKING: sentimentScores.filter((s) => s < -0.1).length,
                        NEUTRAL: sentimentScores.filter((s) => s < 0.1).length,
                        POSITIVE: sentimentScores.filter((s) => s >= 0.1).length
                    }
                };
            });
    }, [activities]);

    const xScale = useMemo(() => {
        const allDates = [
            ...timeSeries.map(x),
            ...followersByDayArr.map((item) => item.datetime),
            ...sentimentData.map((item) => item.date)
        ];
        const xScaleDates = Array.from(new Set(allDates));

        return scaleBand({
            range: [0, width - 100],
            round: true,
            domain: xScaleDates.sort()
        });
    }, [timeSeries, followersByDayArr, sentimentData, width]);

    return (
        <>
            <svg ref={containerRef} width={width} height={height}>
                <Axes
                    xScale={xScale}
                    yScalePadding={
                        style?.price?.yScalePadding ?? DEFAULT_Y_AXIS_SCALE_PADDING
                    }
                    height={height - 50}
                    quotes={timeSeries}
                    width={width}
                    followers={followersByDayArr}
                    overlay={overlay}
                    timeRange={timeRange}
                />
                <FocusedColumn
                    xScale={xScale}
                    focused={tooltipData?.day}
                    height={height - 50 - 32}
                />
                <VolumeChart
                    activities={activities}
                    xScale={xScale}
                    width={width}
                    height={90}
                    top={height - 50 - 90}
                    filterBy={filterBy}
                    isWeek={isWeek}
                />
                {overlay === 'followers' && (
                    <FollowersChart
                        xScale={xScale}
                        height={height - 50}
                        top={0}
                        followers={followersByDayArr}
                    />
                )}

                {overlay === 'sentiment' && (
                    <SentimentChart
                        xScale={xScale}
                        height={height - 50}
                        top={0}
                        sentiment={sentimentData.filter((d) => !!xScale(d.date))}
                    />
                )}

                {overlay === 'volume' && (
                    <TradeVolumeChart
                        xScale={xScale}
                        width={width}
                        height={height - 50}
                        top={0}
                        timeSeries={timeSeries}
                    />
                )}

                <PriceChart
                    xScale={xScale}
                    yScalePadding={
                        style?.price?.yScalePadding ?? DEFAULT_Y_AXIS_SCALE_PADDING
                    }
                    quotes={timeSeries}
                    width={width}
                    height={height - 50}
                    timeRange={timeRange}
                />
                <ActivityMarkers
                    xScale={xScale}
                    activities={activities}
                    top={height - 16}
                />
                <AbnormalReturnsChart />
                <TooltipTargets
                    showTooltip={showTooltip}
                    hideTooltip={hideTooltip}
                    entity={entity}
                    quotes={timeSeries}
                    followers={followersByDayArr}
                    activities={activities}
                    height={height - 50 - 32}
                    xScale={xScale}
                    onClick={onClick}
                    filterBy={filterBy}
                    sentiment={sentimentData}
                />
            </svg>
            {tooltipOpen && tooltipData && (
                <>
                    <TooltipInPortal
                        left={tooltipLeft! + xScale.bandwidth() + 32}
                        top={tooltipTop! - 4}>
                        <Tooltip
                            tooltipData={tooltipData}
                            filterBy={filterBy}
                            overlay={overlay}
                        />
                    </TooltipInPortal>
                </>
            )}
        </>
    );
}
