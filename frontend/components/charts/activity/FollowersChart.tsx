
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { Group } from '@visx/group';
import { scaleLinear } from '@visx/scale';
import { LinePath } from '@visx/shape';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { extent } from '@visx/vendor/d3-array';
import { useMemo } from 'react';

export type FollowerDataPoint = {
    datetime: string;
    twitter: number;
    linkedin: number;
    total: number;
};
export default function FollowersChart({
    xScale,
    height,
    top,
    followers
}: {
    xScale: AnyScaleBand;
    height: number;
    top: number;
    followers: FollowerDataPoint[]
}) {
    const totalFollowers = useMemo(() => {
        return followers.filter((it) => xScale(it.datetime) !== undefined);
    }, [followers, xScale]);

    const yScale = useMemo(() => {
        return scaleLinear({
            range: [height, 0],
            round: true,
            domain: extent(followers, (it) => it.total) as number[]
        });
    }, [followers, height]);
    return (
        <Group left={64 + xScale.bandwidth() / 2} top={top}>
            <LinePath
                stroke="#7620f0"
                strokeWidth={1}
                data={totalFollowers}
                x={(d) => xScale(d.datetime) ?? 0}
                y={(d) => yScale(d.total) ?? 0}
            />
        </Group>
    );
}
