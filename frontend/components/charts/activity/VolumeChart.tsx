import { Group } from '@visx/group';
import { Bar as Bar<PERSON><PERSON> } from '@visx/shape';
import { useMemo } from 'react';

import { formatWithTimeZone } from '@/util/date';
import { Activity } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { scaleLinear } from '@visx/scale';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { extent } from '@visx/vendor/d3-array';
import activityFormat from '@/util/activityFormat';
import { useOrganisation } from '@/components/OrganisationProvider';
import activitySource from '@/util/activitySource';
import hasField from '@/util/hasField';
import { DiscreteSentiment, discreteSentiment, SENTIMENT_COLORS, sentimentScore } from '@/util/sentiment';
import { parseISO } from 'date-fns';
const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function VolumeChart({
    xScale,
    height,
    top,
    activities,
    filterBy,
    isWeek
}: {
    xScale: AnyScaleBand;
    width: number;
    height: number;
    top: number;
    activities: Array<Activity>;
    filterBy: String;
    isWeek: boolean;
}) {
    const organisation = useOrganisation();

    const activityByFormat = useMemo(() => {
        return Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(b[0]).valueOf() - new Date(a[0]).valueOf())
            .filter((it) => !!xScale(it[0]))
            .reduce(
                (acc, [day, activities]) => {
                    const activitiesByType = Object.entries(
                        groupBy(activities, (it) => activityFormat(it))
                    ).reduce(
                        (acc, [group, activities]) => ({
                            ...acc,
                            [group]: activities.length
                        }),
                        {} as Record<string, number>
                    );
                    const dateObj = parseISO(day);
                    const dayLabel = dayNames[dateObj.getDay()];
                    acc.push({
                        date: day,
                        day: dayLabel,
                        totalCount: activities.length,
                        countByType: { ...activitiesByType }
                    });
                    return acc;
                },
                [] as Array<{
                    date: string;
                    day: string;
                    totalCount: number;
                    countByType: Record<string, number>;
                }>
            );
    }, [activities, organisation?.selected?.entity, xScale]);

    const filteractivityBySource = useMemo(() => {
        return Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(b[0]).valueOf() - new Date(a[0]).valueOf())
            .filter((it) => !!xScale(it[0]))
            .reduce(
                (acc, [day, activities]) => {
                    const activitiesByType = Object.entries(
                        groupBy(
                            activities.filter(a => activitySource(a) !== 'Other'),
                            activitySource
                        )
                    ).reduce(
                        (acc, [group, activities]) => ({
                            ...acc,
                            [group]: activities.length
                        }),
                        {} as Record<string, number>
                    );

                    acc.push({
                        day,
                        totalCount: activities.length,
                        countByType: { ...activitiesByType }
                    });
                    return acc;
                },
                [] as Array<{
                    day: string;
                    totalCount: number;
                    countByType: Record<string, number>;
                }>
            );
    }, [activities, organisation?.selected?.entity, xScale]);

    const filteractivityBySentiment = useMemo(() => {
        return Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(b[0]).valueOf() - new Date(a[0]).valueOf())
            .filter((it) => !!xScale(it[0]))
            .map(([day, dayActivities]) => {
                const activitiesWithSentiment = dayActivities.filter(hasField('sentiment'));

                const sentimentScores = activitiesWithSentiment.map((a) =>
                    sentimentScore(a.sentiment)
                );
                const discreteSentiments = sentimentScores.map(discreteSentiment);

                const countByType = {
                    NEGATIVE: discreteSentiments.filter((s) => s === DiscreteSentiment.NEGATIVE).length,
                    LACKING: discreteSentiments.filter((s) => s === DiscreteSentiment.LACKING).length,
                    NEUTRAL: discreteSentiments.filter((s) => s === DiscreteSentiment.NEUTRAL).length,
                    POSITIVE: discreteSentiments.filter((s) => s === DiscreteSentiment.POSITIVE).length
                };

                const total = Object.values(countByType).reduce((sum, val) => sum + val, 0);
                return {
                    day,
                    total,
                    countByType
                };
            })
    }, [activities, organisation?.selected?.entity, xScale])

    const activityColors: Record<string, string> = useMemo(() => {
        return {
            Publication: '#fcdee8',
            Chatter: '#fbf197',
            Broadcast: '#6a7a20',
            Announcement: '#197aff',
            Manual: '#6b7280'
        };
    }, []);

    const sourceColors: Record<string, string> = useMemo(() => {
        return {
            ASX: '#002D72',
            Hotcopper: '#F4B400',
            Reddit: '#FF5700',
            LinkedIn: '#1273DE',
            Twitter: '#1DA1F2'
        };
    }, []);


    const yScale = scaleLinear({
        range: [height, 0],
        round: true,
        domain: extent([...activityByFormat.map((it) => it.totalCount), 0]) as number[]
    });

    const yMaxSentiment = Math.max(...filteractivityBySentiment.map((it) => it.total), 1);

    const yScaleSentiment = scaleLinear<number>({
        range: [height, 0],
        round: false,
        domain: [0, yMaxSentiment]
    });
    return (
        <>
            <Group top={top} left={64}>
                {filterBy === 'format' && activityByFormat.map((d, i) => {
                    const width = xScale.bandwidth();
                    const constrainedWidth = Math.max(Math.min(20, width) - 2, 2);
                    const spacing = (width - constrainedWidth) / 2;
                    const radius = Math.max(constrainedWidth / 6, 2);
                    let barY = height;

                    return Object.entries(d.countByType).map(([type, count], j) => {
                        const barHeight = (yScale(0) ?? 0) - (yScale(count) ?? 0);
                        barY -= barHeight;

                        return (
                            <BarChart
                                key={`${i}-${j}`}
                                x={(xScale(d.date) ?? 0) + spacing}
                                y={barY}
                                width={constrainedWidth}
                                height={barHeight}
                                fill={activityColors[type]}
                                fillOpacity={0.7}
                                rx={radius}
                                ry={radius}
                            />
                        );
                    });
                })}
                {filterBy === 'source' && filteractivityBySource.map((d, i) => {
                    const width = xScale.bandwidth();
                    const constrainedWidth = Math.max(Math.min(20, width) - 2, 2);
                    const spacing = (width - constrainedWidth) / 2;
                    const radius = Math.max(constrainedWidth / 6, 2);
                    let barY = height;

                    return Object.entries(d.countByType).map(([type, count], j) => {
                        const barHeight = (yScale(0) ?? 0) - (yScale(count) ?? 0);
                        barY -= barHeight;
                        return (
                            <BarChart
                                key={`${i}-${j}`}
                                x={(xScale(d.day) ?? 0) + spacing}
                                y={barY}
                                width={constrainedWidth}
                                height={barHeight}
                                fill={sourceColors[type]}
                                fillOpacity={0.7}
                                rx={radius}
                                ry={radius}
                            />
                        );
                    });
                })}

                {filterBy === 'sentiment' && filteractivityBySentiment.map((d, i) => {
                    const width = xScale.bandwidth();
                    const constrainedWidth = Math.max(Math.min(20, width) - 2, 2);
                    const spacing = (width - constrainedWidth) / 2;
                    const radius = Math.max(constrainedWidth / 6, 2);
                    let barY = height;

                    return Object.entries(d.countByType).map(([type, total], j) => {
                        const barHeight = (yScaleSentiment(0) ?? 0) - (yScaleSentiment(total) ?? 0);
                        barY -= barHeight;
                        const sentiment = DiscreteSentiment[type as keyof typeof DiscreteSentiment];
                        return (
                            <BarChart
                                key={`${i}-${j}`}
                                x={(xScale(d.day) ?? 0) + spacing}
                                y={barY}
                                width={constrainedWidth}
                                height={barHeight}
                                fill={SENTIMENT_COLORS[sentiment]}
                                fillOpacity={0.7}
                                rx={radius}
                                ry={radius}
                            />
                        );
                    });
                })}
            </Group>
        </>
    );
}