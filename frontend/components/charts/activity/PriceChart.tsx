import React from 'react';
import { scaleLinear } from '@visx/scale';
import { Group } from '@visx/group';
import { BoxPlot } from '@visx/stats';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { GridRows } from '@visx/grid';
import { LinePath } from '@visx/shape';

export default function PriceChart({
    xScale,
    height,
    width,
    quotes,
    yScalePadding,
    timeRange
}: {
    xScale: AnyScaleBand;
    width: number;
    height: number;
    quotes: Array<TimeSeriesQuote>;
    yScalePadding: number;
    timeRange?: string
}) {
    const x = (quote: TimeSeriesQuote) => quote.datetime;

    const min = (quote: TimeSeriesQuote) => quote.low;
    const max = (quote: TimeSeriesQuote) => quote.high;
    const firstQuartile = (quote: TimeSeriesQuote) => Math.min(quote.close, quote.open);
    const thirdQuartile = (quote: TimeSeriesQuote) => Math.max(quote.close, quote.open);

    const minYValue = Math.min(...quotes.map(min)) * yScalePadding;
    const maxYValue = Math.max(...quotes.map(max));

    const yScale = scaleLinear<number>({
        range: [height - 32, 0],
        round: true,
        domain: [minYValue, maxYValue]
    });

    const boxWidth = xScale.bandwidth();
    const constrainedWidth = Math.max(Math.min(28, boxWidth) - 4, 0);

    return (
        <>
            <Group top={32} left={64}>
                <GridRows
                    left={0}
                    scale={yScale}
                    width={width}
                    strokeDasharray="1,3"
                    stroke="#000"
                    strokeOpacity={0.2}
                    pointerEvents="none"
                />
                {
                    (!timeRange || !['1D', '1W'].includes(timeRange)) ?
                        quotes.map((d, i) => {
                            const spacing = (boxWidth - constrainedWidth) / 2;
                            const radius = Math.max(constrainedWidth / 8, 1);

                            return (
                                <g key={i}>
                                    <BoxPlot
                                        min={min(d)}
                                        max={max(d)}
                                        left={xScale(x(d))! + spacing}
                                        firstQuartile={firstQuartile(d)}
                                        thirdQuartile={thirdQuartile(d)}
                                        boxWidth={constrainedWidth}
                                        fill={d.close >= d.open ? '#059669' : '#e11d48'}
                                        fillOpacity={1}
                                        valueScale={yScale}
                                        strokeWidth={2}
                                        stroke={d.close >= d.open ? '#059669' : '#e11d48'}
                                        ry={radius}
                                        rx={radius}
                                        boxProps={{
                                            strokeWidth: 1
                                        }}
                                    />
                                </g>
                            );
                        })
                        :
                        <>
                            <LinePath
                                data={quotes}
                                x={d => xScale(x(d))! + xScale.bandwidth() / 2}
                                y={d => yScale(d.open)}
                                stroke="#3b82f6" // Blue for open prices
                                strokeWidth={2}
                            />
                            <LinePath
                                data={quotes}
                                x={d => xScale(x(d))! + xScale.bandwidth() / 2}
                                y={d => yScale(d.close)}
                                stroke="#10b981" // Green for close prices
                                strokeWidth={2}
                            />

                        </>}
            </Group>
        </>
    );
}