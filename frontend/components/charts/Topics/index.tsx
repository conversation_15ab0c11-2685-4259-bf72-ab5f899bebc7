import React, { useMemo } from 'react';
import { Topic } from '@quarterback/types';
import { ParentSize } from '@visx/responsive';
import { scaleBand, scaleLinear } from '@visx/scale';
import { Group } from '@visx/group';
import { Bar } from '@visx/shape';
import { GridColumns } from '@visx/grid';
import { AxisBottom } from '@visx/axis';
import { max } from '@visx/vendor/d3-array';
import Tooltip from '@/components/Tooltip';

// Purple color matching the requirement image
const TOPIC_COLOR = '#8B5CF6';

interface TopicsChartProps {
    width: number;
    height: number;
    topics: Array<Topic>;
}

function TopicsChart({ width, height, topics }: TopicsChartProps) {
    // Margins for the chart
    const margin = { top: 20, right: 40, bottom: 40, left: 124 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Sort topics by count in descending order and take top 6
    const sortedTopics = useMemo(() => {
        return [...topics].sort((a, b) => b.count - a.count).slice(0, 6);
    }, [topics]);

    // Scales
    const yScale = useMemo(() => {
        return scaleBand({
            range: [0, innerHeight],
            domain: sortedTopics.map((d) => d.topic),
            padding: 0.3
        });
    }, [sortedTopics, innerHeight]);

    const xScale = useMemo(() => {
        const maxCount = max(sortedTopics, (d) => d.count) || 0;
        return scaleLinear({
            range: [0, innerWidth],
            domain: [0, maxCount],
            nice: true
        });
    }, [sortedTopics, innerWidth]);

    if (sortedTopics.length === 0) {
        return (
            <div className="flex items-center justify-center h-full text-gray-500">
                No topics data available
            </div>
        );
    }

    return (
        <svg width={width} height={height}>
            <Group left={margin.left} top={margin.top}>
                <GridColumns
                    scale={xScale}
                    height={innerHeight}
                    stroke="#e2e8f0"
                    strokeWidth={1}
                    strokeOpacity={0.8}
                    pointerEvents="none"
                    numTicks={5}
                />

                {/* Bars */}
                {sortedTopics.map((topic) => {
                    const barWidth = xScale(topic.count);
                    const barY = yScale(topic.topic) || 0;

                    return (
                        <Bar
                            key={topic.topic}
                            x={0}
                            y={barY}
                            width={barWidth}
                            height={20}
                            fill={TOPIC_COLOR}
                            rx={4}
                            ry={4}
                        />
                    );
                })}

                {/* Y-axis labels with tooltips */}
                {sortedTopics.map((topic) => {
                    const barY = yScale(topic.topic) || 0;
                    const fullText = topic.topic;
                    const truncatedText =
                        fullText?.length > 15
                            ? `${fullText.substring(0, 15)}...`
                            : fullText;
                    const shouldShowTooltip = fullText?.length > 15;

                    return (
                        <Group key={`label-${topic.topic}`}>
                            {shouldShowTooltip ? (
                                <Tooltip text={fullText}>
                                    <text
                                        x={-8}
                                        y={barY + 10}
                                        fill="#64748b"
                                        fontSize={12}
                                        textAnchor="end">
                                        {truncatedText}
                                    </text>
                                </Tooltip>
                            ) : (
                                <text
                                    x={-8}
                                    y={barY + 10}
                                    fill="#64748b"
                                    fontSize={12}
                                    textAnchor="end">
                                    {truncatedText}
                                </text>
                            )}
                        </Group>
                    );
                })}

                {/* X-axis */}
                <AxisBottom
                    top={innerHeight}
                    scale={xScale}
                    stroke="#e2e8f0"
                    tickStroke="#e2e8f0"
                    tickLabelProps={() => ({
                        fill: '#64748b',
                        fontSize: 11,
                        textAnchor: 'middle'
                    })}
                    numTicks={5}
                />
            </Group>
        </svg>
    );
}

interface TopicsProps {
    topics: Array<Topic>;
}

export default function Topics({ topics }: TopicsProps) {
    return (
        <div className="h-80 w-full">
            <ParentSize>
                {({ width, height }) => (
                    <TopicsChart width={width} height={height} topics={topics} />
                )}
            </ParentSize>
        </div>
    );
}
