import { Activity } from '@quarterback/types';
import { useMemo } from 'react';
import { groupBy, isDefined } from '@quarterback/util';
import source from '@/util/source';
import { sentimentScore, discreteSentiment, DiscreteSentiment } from '@/util/sentiment';

const SENTIMENT_COLORS = {
    [DiscreteSentiment.NEGATIVE]: '#f87171', // red-300
    [DiscreteSentiment.LACKING]: '#facc15', // yellow-300
    [DiscreteSentiment.NEUTRAL]: '#e2e8f0', // slate-200
    [DiscreteSentiment.POSITIVE]: '#4ade80' // green-300
};

interface Props {
    activities: Array<Activity>;
}

interface SourceSentimentData {
    source: string;
    total: number;
    bands: [number, number, number, number];
}

const ALLOWED_SOURCES = ['Hotcopper', 'LinkedIn', 'Twitter', 'Reddit'];

export default function SourceSentiments({ activities }: Props) {
    const sourceData = useMemo(() => {
        const activitiesBySource = groupBy(activities, (activity) => source(activity));

        const filteredSources = Object.entries(activitiesBySource).filter(
            ([sourceName]) => ALLOWED_SOURCES.includes(sourceName)
        );

        const sourceDataArray: SourceSentimentData[] = filteredSources.map(
            ([sourceName, sourceActivities]) => {
                const activitiesWithSentiment = sourceActivities.filter(
                    (activity) => activity.sentiment && isDefined(activity.sentiment)
                );

                if (activitiesWithSentiment.length === 0) {
                    return {
                        source: sourceName,
                        total: sourceActivities.length,
                        bands: [0, 0, 1, 0] as [number, number, number, number] // Default to neutral if no sentiment
                    };
                }

                const sentimentCounts = activitiesWithSentiment
                    .map((activity) => sentimentScore(activity.sentiment!))
                    .map((score) => discreteSentiment(score))
                    .reduce(
                        (acc, sentiment) => {
                            acc[sentiment]++;
                            return acc;
                        },
                        [0, 0, 0, 0] as [number, number, number, number]
                    );

                const total = sentimentCounts.reduce((sum, count) => sum + count, 0);
                const bands: [number, number, number, number] = [
                    sentimentCounts[DiscreteSentiment.NEGATIVE] / total,
                    sentimentCounts[DiscreteSentiment.LACKING] / total,
                    sentimentCounts[DiscreteSentiment.NEUTRAL] / total,
                    sentimentCounts[DiscreteSentiment.POSITIVE] / total
                ];

                return {
                    source: sourceName,
                    total: sourceActivities.length,
                    bands
                };
            }
        );

        const allSourcesData: SourceSentimentData[] = ALLOWED_SOURCES.map(
            (sourceName) => {
                const existingData = sourceDataArray.find(
                    (data) => data.source === sourceName
                );
                return (
                    existingData || {
                        source: sourceName,
                        total: 0,
                        bands: [0, 0, 1, 0] as [number, number, number, number] // Default to neutral
                    }
                );
            }
        );

        return allSourcesData;
    }, [activities]);

    if (sourceData.length === 0) {
        return (
            <div className="flex items-center justify-center h-32 text-gray-500">
                No source data available
            </div>
        );
    }

    return (
        <div className="space-y-4 p-4">
            {sourceData.map((data) => (
                <div key={data.source} className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 truncate">
                            {data.source}
                        </span>
                        <span className="text-xs text-gray-500 ml-2">
                            {data.total > 0 ? data.total : 'No data'}
                        </span>
                    </div>
                    <div className="w-full h-3 bg-gray-100 rounded-full overflow-hidden flex">
                        {data.bands.map((proportion, index) => {
                            if (proportion === 0) return null;

                            const sentimentType = index as DiscreteSentiment;
                            const color = SENTIMENT_COLORS[sentimentType];
                            const widthPercentage = proportion * 100;

                            return (
                                <div
                                    key={index}
                                    className="h-full transition-all duration-200"
                                    style={{
                                        backgroundColor: color,
                                        width: `${widthPercentage}%`
                                    }}
                                    title={`${(proportion * 100).toFixed(1)}%`}
                                />
                            );
                        })}
                    </div>
                </div>
            ))}
        </div>
    );
}
