import {
    CalendarDaysIcon,
    CheckCircleIcon,
    TvIcon,
    UsersIcon,
    VideoCameraIcon
} from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { useMemo } from 'react';

export function ActivityFormatIndicator({ activity }: { activity: string }) {
    const [bgColor, textColor] = useMemo(() => {
        switch (activity) {
            case 'media':
                return ['bg-qb-red-70', 'text-qb-red-100'];
            case 'announcement':
                return ['bg-blue-100', 'text-blue-600'];
            case 'broadcast':
                return ['bg-qb-green-30', 'text-qb-green-70'];
            case 'chatter':
                return ['bg-yellow-100', 'text-yellow-700'];
            default:
                return ['bg-qb-gray-50', 'text-qb-gray-150'];
        }
    }, [activity]);

    const formatIcon = useMemo(() => {
        switch (activity) {
            case 'call':
                return <VideoCameraIcon className="size-4" />;
            case 'meeting':
                return <UsersIcon className="size-4" />;
            case 'presentation':
                return <TvIcon className="size-4" />;
            case 'event':
                return <CalendarDaysIcon className="size-4" />;
            case 'other':
                return <CheckCircleIcon className="size-4" />;
            default:
                return null;
        }
    }, [activity]);

    return (
        <span
            className={classNames(
                bgColor,
                textColor,
                'inline-flex items-center rounded-[7px] gap-x-1 px-2 py-1 text-sm bg-qb-gray-50 border-qb-gray-115 font-medium ring-1 ring-inset ring-gray-300 capitalize'
            )}>
            {formatIcon}
            {activity}
        </span>
    );
}

export default ActivityFormatIndicator;
