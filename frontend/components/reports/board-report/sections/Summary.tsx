import SummaryStatistic from '@/components/reports/board-report/SummaryStatistic';
import { View } from '@react-pdf/renderer';
import React from 'react';

interface Props {
    price?: {
        current: number;
        change: number;
    };
    activities: number;
    followers: number;
    cumulativeAbnormalReturns: number;
}

export default function Summary({
    price,
    cumulativeAbnormalReturns,
    activities,
    followers
}: Props) {
    return (
        <View
            style={{
                display: 'flex',
                flexDirection: 'row'
            }}>
            <SummaryStatistic
                label="Share price"
                value={`$${price?.current.toFixed(4)}`}
                change={price?.change}
                style={{ flex: 1 }}
            />
            <SummaryStatistic
                label="Abnormal returns"
                value={`${Math.abs(cumulativeAbnormalReturns * 100).toFixed(1)}%`}
                style={{ flex: 1 }}
            />
            <SummaryStatistic
                label="Activities"
                value={`${activities}`}
                style={{ flex: 1 }}
            />
            <SummaryStatistic
                label="Followers"
                value={`${followers}`}
                style={{ flex: 1 }}
            />
        </View>
    );
}
