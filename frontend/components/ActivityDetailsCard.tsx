import Tooltip from '@/components/Tooltip';
import useActivitySummary from '@/util/useActivitySummary';
import {
    EyeIcon,
    HandThumbUpIcon,
    UserIcon,
    VideoCameraIcon
} from '@heroicons/react/24/outline';
import { Activity, HotCopperPost } from '@quarterback/types';
import classNames from 'classnames';
import { formatInTimeZone } from 'date-fns-tz';
import { useMemo, useState } from 'react';
import truncate from '../../util/dist/truncate';
import { getSourceIconFromURL, onImageError } from './ActivitySourceIcon';
import ActivityFormatIndicator from './ActivityFormatIndicator';
import SentimentIndicator from '@/app/(authenticated)/investors/activities/SentimentIndicator';
import activityFormat from '@/util/activityFormat';

function ActivityAvatar({
    activity,
    className = ''
}: {
    activity: Activity;
    className: string;
}) {
    switch (activity.type) {
        case 'hotcopper':
            return (
                <img
                    src="https://hotcopper.com.au/images/default-avatar-l.png"
                    className={className}
                />
            );

        case 'asx-announcement':
            return <img src="/sources/asx.svg" className={className} />;

        case 'tweet':
            return <img src="/sources/x.svg" className={className} />;

        case 'reddit':
        case 'redditComment':
            return <img src="/sources/reddit.svg" className={className} />;

        case 'linkedIn':
            return <img src="/sources/linkedin.svg" className={className} />;

        case 'call':
        case 'meeting':
        case 'presentation':
        case 'event':
        case 'other':
            return <UserIcon className="size-4" />;

        default:
            if (activity.url) {
                return (
                    <img
                        src={getSourceIconFromURL(activity.url)}
                        className={className}
                        onError={(e) => onImageError(e, activity)}
                        alt=""
                    />
                );
            }

            return (
                <div
                    className={classNames(
                        'aspect-square rounded-full bg-gray-200',
                        className
                    )}
                />
            );
    }
}

function HotCopperStatus({ activity }: { activity: HotCopperPost }) {
    const disclosure = useMemo(() => {
        switch (activity.hotcopper?.disclosure) {
            case 'HELD':
                return 'Held';
            case 'NOT_HELD':
                return 'Not held';
            case 'UNDISCLOSED':
            default:
                return 'Undisclosed';
        }
    }, [activity]);

    const sentiment = useMemo(() => {
        switch (activity.hotcopper?.sentiment) {
            case 'BUY':
                return 'Buy';
            case 'HOLD':
                return 'Hold';
            case 'SELL':
                return 'Sell';
            case 'NONE':
            default:
                return 'None';
        }
    }, [activity]);

    return (
        <div className="flex gap-x-2">
            <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                {disclosure}
            </span>
            <span className="inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10">
                {sentiment}
            </span>
        </div>
    );
}

function ActivityStats({ activity }: { activity: Activity }) {
    const views = useMemo(() => {
        switch (activity.type) {
            case 'hotcopper':
                return activity.thread.views;
            case 'tweet':
                return activity.views;
            default:
                return undefined;
        }
    }, [activity]);

    const likes = useMemo(() => {
        if ('likes' in activity) return activity.likes;
        if ('score' in activity) return activity.score;

        return undefined;
    }, [activity]);

    return (
        <div className="flex justify-between mx-2">
            <div className="flex items-center gap-x-2 text-gray-500">
                {views !== undefined && (
                    <Tooltip
                        text={
                            activity.type === 'hotcopper' ? 'Total thread views' : 'Views'
                        }>
                        <div className="flex items-center gap-x-2 cursor-help">
                            <EyeIcon className="size-5 text-gray-600" />
                            <span className="">{views.toLocaleString()}</span>
                        </div>
                    </Tooltip>
                )}

                {likes !== undefined && (
                    <Tooltip text="Likes">
                        <div className="flex items-center gap-x-2 cursor-help">
                            <HandThumbUpIcon className="size-5 text-gray-600" />
                            <span className="">{likes.toLocaleString()}</span>
                        </div>
                    </Tooltip>
                )}
            </div>
            {activity.type === 'hotcopper' && <HotCopperStatus activity={activity} />}
        </div>
    );
}

interface Props {
    activity: Activity;
    className?: string;
    imgClassNames?: string;
    show?: {
        title?: boolean;
        image?: boolean;
    };
}

export default function ActivityDetailsCard({
    activity,
    show,
    className,
    imgClassNames
}: Props) {
    const { description, url, image, title, author } = useActivitySummary(activity);
    const [imgError, setImgError] = useState(false);
    const CHARS_LIMIT = 650;

    if (!activity) return null;

    return (
        <div
            className={classNames(className, {
                'bg-transparent': !className?.match(/\bbg-[^\s]+/),
                'p-2': !className?.match(/\bp-[^\s]+/)
            })}>
            <div>
                {title && (show?.title === undefined || show?.title === true) && (
                    <div className="mt-4">
                        <span className="font-medium text-lg">{title}</span>
                        {activity.type === 'asx-announcement' &&
                            activity.priceSensitive && (
                                <span className="text-red-500"> $</span>
                            )}
                    </div>
                )}
                <div className="flex gap-2 my-2">
                    <span className="border border-gray-200 rounded-[7px] px-2 inline-flex items-center">
                        <ActivityAvatar
                            activity={activity}
                            className="rounded-sm h-3 w-3 object-cover"
                        />
                        {author && (
                            <span className="text-sm text-qb-black-100 mx-2">
                                {author}
                            </span>
                        )}
                    </span>
                    {activity.type === 'hotcopper' && (
                        <HotCopperStatus activity={activity} />
                    )}
                    <ActivityFormatIndicator activity={activityFormat(activity)} />
                </div>
                <div className="flex justify-between items-center my-4">
                    <span className="text-xs text-qb-gray-150">
                        {formatInTimeZone(
                            activity?.posted,
                            'Australia/Sydney',
                            'd MMMM, yyyy HH:mm zzz'
                        )}
                    </span>
                    {activity.sentiment && (
                        <SentimentIndicator
                            sentiment={activity.sentiment}
                            showIcon={true}
                            className="text-xs font-medium"
                        />
                    )}
                </div>
            </div>
            {image &&
                !imgError &&
                (show?.image === undefined || show?.image === true) && (
                    <div className={classNames(imgClassNames, '-mx-4 my-4 mb-6')}>
                        <img
                            src={image}
                            className="w-full h-72 object-cover -mb-4"
                            onError={() => setImgError(true)}
                        />
                    </div>
                )}
            {description && (
                <>
                    <p className="whitespace-pre-line mx-2 text-sm text-qb-gray-150 font-normal">
                        {truncate(description, CHARS_LIMIT, false)}
                    </p>
                </>
            )}
        </div>
    );
}
