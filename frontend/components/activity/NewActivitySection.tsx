import { useActivityForm } from '@/app/(authenticated)/investors/activities/contexts/ActivityFormProvider';
import Button from '@/components/ui/Button';
import ManualActivityForm from '@/app/(authenticated)/investors/activities/forms/ManualActivityForm';
import { PlusIcon } from '@heroicons/react/24/outline';

interface NewActivitySectionProps {
    buttonSize?: 'sm' | 'md' | 'lg';
    buttonClassName?: string;
    disabled?: boolean;
}

export default function NewActivitySection({
    buttonSize = 'sm',
    buttonClassName,
    disabled = false
}: NewActivitySectionProps) {
    const { openForm } = useActivityForm();

    return (
        <>
            <Button
                variant="primary"
                onClick={openForm}
                size={buttonSize}
                disabled={disabled}
                icon={<PlusIcon className="size-4" />}
                className={buttonClassName}>
                New Activity
            </Button>
            <ManualActivityForm />
        </>
    );
}
