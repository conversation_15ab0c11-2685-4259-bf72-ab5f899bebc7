import { Fragment } from 'react';
import { Transition } from '@headlessui/react';
import { XCircleIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';

export default function RightPanel({
    children,
    open,
    onClose,
}: {
    open: boolean;
    onClose: () => void;
    children: React.ReactNode;
}) {
    return (
        <>
            {/* Close Button */}
            <Transition
                show={open}
                as={Fragment}
                enter="transition-opacity transform duration-300 ease-out"
                enterFrom="opacity-0 -translate-y-2"
                enterTo="opacity-100 translate-y-0"
                leave="transition-opacity transform duration-200 ease-in"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 -translate-y-2"
            >
                <button
                    onClick={onClose}
                    className="fixed z-50 top-[75px] right-[32.5rem] rounded-full "
                >
                    <XCircleIcon className="w-6 h-6" />
                </button>
            </Transition>

            {/* Right-side panel */}
            <Transition
                show={open}
                as={Fragment}
                enter="transition-transform duration-300 ease-out"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transition-transform duration-200 ease-in"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
            >
                <div
                    className={classNames(
                        'fixed top-[60px] bottom-0 right-0 w-[32rem] overflow-y-auto border-l border-gray-300 bg-white shadow-xl z-40'
                    )}
                    style={{ pointerEvents: open ? 'auto' : 'none' }}
                >
                    {children}
                </div>
            </Transition>
        </>
    );
}
