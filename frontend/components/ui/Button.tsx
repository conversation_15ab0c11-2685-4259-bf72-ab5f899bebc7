import React from 'react';
import classNames from 'classnames';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: 'primary' | 'secondary' | 'tertiary';
    icon?: React.ReactNode;
    fullWidth?: boolean;
    size?: 'sm' | 'md' | 'lg';
    active?: boolean;
}

export default function Button({
    children,
    variant = 'primary',
    icon,
    fullWidth = false,
    active = false,
    size = 'md',
    className,
    ...props
}: ButtonProps) {
    const baseStyles =
        'inline-flex items-center justify-center font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2';

    const variantStyles = {
        primary:
            'bg-indigo-600 hover:bg-indigo-700 text-white focus:ring-transparent shadow-sm',
        secondary:
            'bg-white hover:bg-gray-100 text-gray-800 focus:ring-transparent border border-gray-200 shadow-sm',
        tertiary:
            'bg-transparent my-1 text-qb-gray-150 hover:bg-gray-100 text-gray-800 focus:ring-transparent'
    };

    const sizeStyles = {
        sm: 'text-sm px-3 py-1',
        md: 'text-base px-4 py-2',
        lg: 'text-lg px-5 py-2.5'
    };

    return (
        <span
            className={classNames({
                'border-b-2 border-black': variant === 'tertiary' && active
            })}>
            <button
                className={classNames(
                    baseStyles,
                    variantStyles[variant],
                    sizeStyles[size],
                    fullWidth ? 'w-full' : '',
                    active &&
                        variant === 'tertiary' &&
                        'border border-gray-200 !text-qb-black-100',
                    className
                )}
                {...props}>
                {icon && <span className="mr-2">{icon}</span>}
                {children}
            </button>
        </span>
    );
}
