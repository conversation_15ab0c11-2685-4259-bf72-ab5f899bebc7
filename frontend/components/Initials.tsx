import React from 'react';

export interface InitialsProps {
    name?: string;
    color?: string;
    hide?: boolean;
}

function Initials({ name, color = '#000', hide = false }: InitialsProps) {
    if (!name || name.length === 0 || !color || color.length === 0 || hide) return null;

    return (
        <div
            className={`flex h-5 w-5 items-center justify-center rounded-md text-white font-semibold text-[10px] uppercase`}
            style={{ backgroundColor: color }}>
            {name[0]}
        </div>
    );
}

export default Initials;
