import { Fragment } from 'react';
import {
    Listbox,
    ListboxButton,
    ListboxOption,
    ListboxOptions,
    Transition
} from '@headlessui/react';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/20/solid';
import classNames from 'classnames';
import { useOrganisation } from '@/components/OrganisationProvider';
import { ListedEntity, Organisation } from '@quarterback/types';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

export default function CompanyPicker({ className = '' }: { className?: string }) {
    const { selected, select, organisations } = useOrganisation();

    function handleChange({
        organisation,
        entity
    }: {
        organisation: Organisation;
        entity: ListedEntity;
    }) {
        select(organisation, entity);
    }

    return (
        <Listbox
            value={selected}
            by={(a, b) =>
                a.entity.symbol === b.entity.symbol &&
                a.entity.exchange === b.entity.exchange &&
                a.organisation.id === b.organisation.id
            }
            onChange={handleChange}>
            {({ open }) => (
                <>
                    <div className={classNames(className, 'relative h-full')}>
                        <ListboxButton className="relative w-full py-1.5 pl-3 pr-3 text-left text-gray-900 sm:text-sm sm:leading-6 flex items-center cursor-pointer h-full">
                            <span className="bg-red-600 text-white rounded-md px-1 py-2 mr-2 whitespace-nowrap">
                                {selected?.entity.symbol}
                            </span>

                            <span className="font-semibold text-xl truncate whitespace-nowrap overflow-hidden flex-1">
                                {selected?.entity.name}
                            </span>

                            <span className="pointer-events-none px-2 flex-shrink-0">
                                <ChevronDownIcon className="h-5 w-5" />
                            </span>
                        </ListboxButton>

                        <Transition
                            show={open}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0">
                            <ListboxOptions
                                anchor={{ to: 'bottom', gap: 8 }}
                                className="absolute z-10 -mt-1 max-h-64 w-72 ml-4 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm divide-y divide-gray-200">
                                {organisations.map((organisation) => (
                                    <div key={organisation.id}>
                                        <div className="text-xs font-medium text-gray-400 mt-2 pl-3 pr-9">
                                            {organisation.name}
                                        </div>
                                        {(organisation.users?.[0]?.entities ?? []).map(
                                            (entity) => (
                                                <ListboxOption
                                                    key={`${entity.symbol}:${entity.exchange}`}
                                                    className={({ focus }) =>
                                                        classNames(
                                                            {
                                                                'bg-gray-50':
                                                                    selected || focus,
                                                                'text-gray-900': !focus
                                                            },
                                                            'relative cursor-default select-none py-2 pl-3 pr-9'
                                                        )
                                                    }
                                                    value={{ organisation, entity }}>
                                                    {({ selected, focus }) => (
                                                        <>
                                                            <span
                                                                className={classNames(
                                                                    selected
                                                                        ? 'font-semibold'
                                                                        : 'font-normal',
                                                                    'block truncate'
                                                                )}>
                                                                {entity.name}
                                                            </span>

                                                            {selected ? (
                                                                <span
                                                                    className={classNames(
                                                                        'text-gray-900',
                                                                        'absolute inset-y-0 right-0 flex items-center pr-4'
                                                                    )}>
                                                                    <CheckIcon
                                                                        className="h-5 w-5"
                                                                        aria-hidden="true"
                                                                    />
                                                                </span>
                                                            ) : null}
                                                        </>
                                                    )}
                                                </ListboxOption>
                                            )
                                        )}
                                    </div>
                                ))}
                            </ListboxOptions>
                        </Transition>
                    </div>
                </>
            )}
        </Listbox>
    );
}
