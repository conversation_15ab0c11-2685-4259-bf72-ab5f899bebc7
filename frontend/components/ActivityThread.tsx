import useActivityThread from '@/api/hooks/useActivityThread';
import { isDefined } from '@quarterback/util';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { z } from 'zod';
import ActivityDetailsCard from './ActivityDetailsCard';

const ActivityThreadProps = z.object({
    threadId: z.string().optional(),
    selectedActivity: z.string().optional()
});

type ActivityThreadProps = z.infer<typeof ActivityThreadProps>;

export default function ActivityThread({
    threadId,
    selectedActivity
}: ActivityThreadProps) {
    const [showAll, setShowAll] = useState<boolean>(false);
    const [loading, setLoading] = useState(true);

    const activityThread = useActivityThread(threadId)?.reverse();

    const selectedIndex = activityThread?.findIndex((it) => it.id === selectedActivity);

    const previousIndex = selectedIndex - 1 > 0 ? selectedIndex - 1 : undefined;

    useEffect(() => {
        if (selectedActivity && isDefined(selectedIndex) && activityThread?.length >= 1) {
            setLoading(false);
        }
    }, [selectedIndex, activityThread?.length, selectedActivity]);

    if (loading) {
        return null;
    }

    if (!loading) {
        return (
            <>
                <ActivityDetailsCard
                    activity={activityThread[0]}
                    show={{ title: true, image: true }}
                />

                {selectedIndex > 1 ? (
                    <div
                        className="flex items-center justify-center text-sm font-medium text-indigo-600  py-1"
                        role="button"
                        onClick={() => setShowAll(!showAll)}>
                        {showAll ? 'Collapse thread' : 'View entire thread'}
                    </div>
                ) : null}

                {showAll ? (
                    <>
                        {activityThread.slice(1).map((it, index) => (
                            <ActivityDetailsCard
                                key={it.id}
                                activity={it}
                                show={{ title: false }}
                                className={classNames(
                                    'border my-2 border-gray-200 rounded-lg',
                                    {
                                        'bg-purple-60': it.id === selectedActivity
                                    }
                                )}
                            />
                        ))}
                    </>
                ) : null}

                {!showAll && previousIndex ? (
                    <ActivityDetailsCard
                        activity={activityThread[previousIndex]}
                        show={{ title: false }}
                        className={'border my-2 border-gray-200 rounded-lg'}
                    />
                ) : null}

                {!showAll && selectedIndex > 0 ? (
                    <ActivityDetailsCard
                        activity={activityThread[selectedIndex]}
                        show={{ title: false }}
                        className="bg-purple-60 border my-2 border-gray-200 rounded-lg"
                    />
                ) : null}
            </>
        );
    }
}
