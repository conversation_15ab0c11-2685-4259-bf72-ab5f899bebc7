import { WordCloudItem } from '@quarterback/types';
import { useMemo } from 'react';
import ReactWordcloud from 'react-wordcloud';

function WordCloud({ wordCloud }: { wordCloud: Array<WordCloudItem> }) {
    const words = useMemo(
        () =>
            wordCloud.map(({ word: text, frequency: value, ...rest }) => ({
                text,
                value,
                ...rest
            })),
        [wordCloud]
    );

    return (
        <ReactWordcloud
            words={words}
            options={{
                enableTooltip: false,
                deterministic: false,
                fontFamily: 'Inter',
                fontSizes: [10, 60],
                fontStyle: 'normal',
                fontWeight: 'normal',
                padding: 1,
                rotations: 3,
                rotationAngles: [0, 90],
                scale: 'sqrt',
                spiral: 'archimedean',
                transitionDuration: 1000
            }}
        />
    );
}

export default WordCloud;
