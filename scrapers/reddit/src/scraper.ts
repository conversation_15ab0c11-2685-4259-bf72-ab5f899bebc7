import { <PERSON><PERSON><PERSON>, Publisher, Scraper } from '@quarterback/scraper-utils';
import { Activity2 } from '@quarterback/types';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import { log } from '@quarterback/util/gcp';
import z from 'zod';

const USER_AGENT = 'backend:au.qback.reddit:v1.0.0 (by /u/BarnacleHappy7791)';
const CLIENT_ID = 'Rt5v_cFa0sdRDbxayZ0NAg';
const CLIENT_SECRET = 'tLrNBAhvNp0fFbNvMvc6G0sI7ePe-A';

const moreSchema = z.object({
    kind: z.literal('more')
});

type More = z.infer<typeof moreSchema>;

const commentDataSchema = z.object({
    id: z.string(),
    score: z.number(),
    created: z.number(),
    body: z.string(),
    author: z.string(),
    parent_id: z.string(),
    name: z.string()
});

const baseCommentSchema = z.object({
    kind: z.literal('t1'),
    data: commentDataSchema
});

const commentSchema = baseCommentSchema.extend({
    data: commentDataSchema.merge(
        z.object({
            replies: z.lazy(() => z.union([commentListing, z.literal('')]).optional())
        })
    )
});

type Comment = z.infer<typeof baseCommentSchema> & {
    data: z.infer<typeof commentDataSchema> & {
        replies?: Listing<Comment | More> | '';
    };
};

const mediaMetaDataSchema = z
    .record(
        z.object({
            status: z.string(),
            e: z.string(),
            m: z.string(),
            p: z
                .array(
                    z.object({
                        x: z.number(),
                        y: z.number(),
                        u: z.string()
                    })
                )
                .optional(),
            s: z
                .object({
                    x: z.number(),
                    y: z.number(),
                    u: z.string()
                })
                .optional(),
            id: z.string()
        })
    )
    .optional();

type MediaMetaData = z.infer<typeof mediaMetaDataSchema>;

const postSchema = z.object({
    kind: z.literal('t3'),
    data: z.object({
        id: z.string(),
        subreddit: z.string(),
        title: z.string(),
        selftext: z.string(),
        author: z.string(),
        score: z.number(),
        upvote_ratio: z.number(),
        created: z.number(),
        url: z.string(),
        num_comments: z.number(),
        name: z.string(),
        media_metadata: mediaMetaDataSchema
    })
});

interface Listing<T> {
    kind: 'Listing';
    data: {
        children: Array<T>;
    };
}

const commentListing: z.ZodType<Listing<Comment | More>> = listingSchema(
    z.union([commentSchema, moreSchema])
);

function listingSchema<ItemType extends z.ZodTypeAny>(itemSchema: ItemType) {
    return z.object({
        kind: z.literal('Listing'),
        data: z.object({
            children: z.array(itemSchema)
        })
    });
}

const searchListingSchema = listingSchema(
    z.discriminatedUnion('kind', [postSchema, moreSchema])
);

const postListingSchema = z.tuple([listingSchema(postSchema), commentListing]);

export interface RateLimiter {
    available(): Promise<true>;
}

function isComment(it: Comment | More): it is Comment {
    return it.kind === 't1';
}

function flattenComments(
    comments: Listing<Comment | More> | '' | undefined
): Array<Comment> {
    return comments === '' || comments === undefined
        ? []
        : comments.data.children
              .filter(isComment)
              .reduce(
                  (comments, comment) => [
                      ...comments,
                      comment,
                      ...flattenComments(comment.data.replies)
                  ],
                  [] as Array<Comment>
              );
}

interface Credentials {
    access_token: string;
    token_type: 'bearer';
    expires_in: number;
    scope: string;
}

export class RedditScraper extends Scraper {
    rateLimiter: RateLimiter;
    fetcher: Fetcher;
    query: string;

    constructor(
        symbol: string,
        exchange: string,
        query: string,
        publisher: Publisher<PubSubMessage2>,
        fetcher: Fetcher,
        rateLimiter: RateLimiter
    ) {
        super(symbol, exchange, publisher);

        this.fetcher = fetcher;
        this.query = query;
        this.rateLimiter = rateLimiter;
    }

    async authenticate(): Promise<Credentials> {
        const response = await this.fetcher.fetch(
            'https://www.reddit.com/api/v1/access_token',
            {
                method: 'POST',
                body: 'grant_type=client_credentials',
                headers: {
                    'User-Agent': USER_AGENT,
                    'Content-Type': 'application/x-www-form-urlencoded',
                    Authorization: `Basic ${Buffer.from(CLIENT_ID + ':' + CLIENT_SECRET).toString('base64')}`
                }
            }
        );

        if (response.ok) {
            return (await response.json()) as Credentials;
        } else {
            log('ERROR', 'Reddit authentication failed');
            throw new Error('Authentication failed');
        }
    }

    async posts(credentials: Credentials) {
        const params = new URLSearchParams({
            q: this.query,
            restrict_sr: 'true',
            limit: '100',
            sort: 'new'
        });

        const url = new URL(`https://oauth.reddit.com/search.json?${params.toString()}`);

        const response = await this.fetcher.fetch(url, {
            headers: {
                'User-Agent': USER_AGENT,
                Authorization: `Bearer ${credentials.access_token}`
            }
        });

        if (response.ok) {
            return searchListingSchema.parse(await response.json());
        } else {
            log('ERROR', 'Search endpoint request failed');
            throw new Error(`Search error not yet handled: ${await response.text()}`);
            // Update the rate limiter and retry when next available
        }
    }

    getImageUrl(mediaMetaData: MediaMetaData | undefined) {
        if (mediaMetaData) {
            const url: string | undefined = Object.values(mediaMetaData)?.filter(
                (it) => it.status === 'valid' && it?.e === 'Image'
            )?.[0]?.s?.u;
            return url && url?.length > 0
                ? decodeURI(url)?.replace(/&amp;/g, '&')
                : undefined;
        }

        return undefined;
    }

    async post(credentials: Credentials, post: string) {
        const url = new URL(`https://oauth.reddit.com/comments/${post}.json`);

        const response = await this.fetcher.fetch(url, {
            headers: {
                'User-Agent': USER_AGENT,
                Authorization: `bearer ${credentials.access_token}`
            }
        });

        if (response.ok) {
            const body = await response.json();
            return postListingSchema.parse(body);
        } else {
            log('ERROR', 'Post endpoint request failed');
            throw new Error(`Post error not yet handled: ${await response.text()}`);
            // Update the rate limiter and retry when next available
        }
    }

    async *chunk(): AsyncGenerator<Array<Activity2>, void, void> {
        if (this.exchange === 'ASX') {
            const credentials = await this.authenticate();

            const activities: Array<Activity2> = [];
            const posts = await this.posts(credentials);

            for (const post of posts.data.children) {
                if ('data' in post && post.data !== undefined) {
                    const comments =
                        post.data.num_comments > 0
                            ? await this.rateLimiter
                                  .available()
                                  .then(() =>
                                      this.post(credentials, post.data!.id).then((post) =>
                                          flattenComments(post[1])
                                      )
                                  )
                            : [];

                    activities.push(
                        {
                            url: post.data.url,
                            title: post.data.title,
                            posted: new Date(post.data.created * 1000),
                            body: post.data.selftext,
                            image: this.getImageUrl(post.data.media_metadata),
                            author: {
                                key: `reddit__${post.data.author}`,
                                name: post.data.author,
                                userId: post.data.author
                            },
                            redditPost: {
                                id: post.data.name,
                                subreddit: post.data.subreddit,
                                score: post.data.score,
                                ratio: post.data.upvote_ratio
                            },
                            symbol: this.symbol,
                            exchange: this.exchange
                        },
                        ...comments.map((comment) => ({
                            url: `https://www.reddit.com/r/${post.data.subreddit}/comments/${post.data.id}/comment/${comment.data.id}`,
                            posted: new Date(comment.data.created * 1000),
                            body: comment.data.body,
                            author: {
                                key: `reddit__${comment.data.author}`,
                                name: comment.data.author,
                                userId: comment.data.author
                            },
                            redditComment: {
                                id: comment.data.name,
                                post: post.data.name,
                                parent: comment.data.parent_id,
                                score: comment.data.score
                            },
                            symbol: this.symbol,
                            exchange: this.exchange
                        }))
                    );
                }
            }

            yield activities;
        } else {
            yield [];
        }
    }
}
