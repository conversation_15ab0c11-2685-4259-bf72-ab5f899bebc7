import { Fetcher } from '@quarterback/scraper-utils/fetchers';
import { Publisher } from '@quarterback/scraper-utils/publishers';
import { Scraper } from '@quarterback/scraper-utils/scrapers';
import {
    Activity2,
    NewsCatcherResponse,
    NewsCatcherSearchParams
} from '@quarterback/types';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import { chunked } from '@quarterback/util';
import { log } from '@quarterback/util/gcp';

type NewsActivity = Activity2 & Required<Pick<Activity2, 'media'>>;

const TOKEN = process.env['NEWSCATCHER_API_KEY'] || '';

export class NewsScraper extends Scraper {
    fetcher: Fetcher;
    params: NewsCatcherSearchParams;

    constructor(
        symbol: string,
        exchange: string,
        params: string,
        publisher: Publisher<PubSubMessage2>,
        fetcher: Fetcher
    ) {
        super(symbol, exchange, publisher);

        this.fetcher = fetcher;
        this.params = NewsCatcherSearchParams.parse(
            JSON.parse(Buffer.from(params, 'base64').toString('utf-8'))
        );
    }

    async *chunk() {
        try {
            const response = NewsCatcherResponse.parse(
                await (
                    await this.fetcher.fetch(
                        'https://v3-api.newscatcherapi.com/api/search',
                        {
                            headers: {
                                'x-api-token': TOKEN,
                                'Content-Type': 'application/json'
                            },
                            method: 'POST',
                            body: JSON.stringify(this.params)
                        }
                    )
                ).json()
            );

            for (const chunk of chunked(response.articles, 20)) {
                yield await Promise.allSettled(
                    chunk.map(async (article): Promise<NewsActivity> => {
                        const source = {
                            url: `https://${article.full_domain_url}`,
                            name: article?.name_source ?? article.full_domain_url,
                            logo: null
                        };

                        return {
                            posted: new Date(article.published_date),
                            url: article.link,
                            title: article.title,
                            body: article.content,
                            media: {
                                image:
                                    article.media &&
                                    !/logo(s)?|\.ico$/i.test(article.media)
                                        ? article.media
                                        : undefined,
                                source: {
                                    name: source.name,
                                    domain: source.url
                                }
                            },
                            symbol: this.symbol,
                            exchange: this.exchange
                        };
                    })
                ).then((it) =>
                    it
                        .filter((it) => it.status === 'fulfilled')
                        .map((it) => (it as PromiseFulfilledResult<NewsActivity>).value)
                );
            }
        } catch (error) {
            log('WARNING', `News Scraper timed out for symbol: ${this.symbol} ${error}`);
        }
    }
}
