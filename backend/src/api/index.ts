import { Router } from 'express';
import quote from './quote.js';
import timeSeries from './timeSeries.js';
import followers from './followers.js';
import organisations from './organisations/index.js';
import user from './user/index.js';
import alerts from './alerts/index.js';
import insights from './insights/index.js';
import search from './search/index.js';
import sources from './sources/index.js';
import authors from './authors/index.js';
import files from './files/index.js';
import entities from './entities.js';

const router: Router = Router();

router.get('/quote', quote);
router.get('/time-series', timeSeries);

router.use('/followers', followers);
router.use('/organisations', organisations);
router.use('/user', user);
router.use('/alerts', alerts);
router.use('/insights', insights);
router.use('/search', search);
router.use('/sources', sources);
router.use('/authors', authors);
router.use('/files', files);
router.use('/entities', entities);

export default router;
