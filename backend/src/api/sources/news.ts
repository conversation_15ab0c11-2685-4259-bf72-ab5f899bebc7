import { Router } from 'express';
import db from '../../db/index.js';
import { newsSource } from '../../db/schema/activities.js';
import { ilike, sql } from 'drizzle-orm';
import z from 'zod';

const router: Router = Router();

const QuerySchema = z.object({
    limit: z.coerce.number().min(1).max(100).default(10),
    offset: z.coerce.number().min(0).default(0),
    search: z.string().optional()
});

router.get('/', async (request, response) => {
    try {
        const { limit, offset, search } = QuerySchema.parse(request.query);

        const whereCondition = search ? ilike(newsSource.name, `%${search}%`) : undefined;

        const countResult = await db
            .select({
                count: sql<number>`count(*)`
            })
            .from(newsSource)
            .where(whereCondition);

        const totalCount = countResult[0]?.count || 0;

        const sources = await db
            .select({
                url: newsSource.url,
                name: newsSource.name,
                logo: newsSource.logo
            })
            .from(newsSource)
            .where(whereCondition)
            .limit(limit)
            .offset(offset)
            .orderBy(newsSource.name);

        return response.status(200).json({
            sources,
            pagination: {
                total: totalCount,
                limit,
                offset
            }
        });
    } catch (error) {
        console.error('Error fetching media sources:', error);
        return response.status(500).json({ error: 'Failed to fetch media sources' });
    }
});

export default router;
