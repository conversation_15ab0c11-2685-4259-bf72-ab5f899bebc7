import { Router } from 'express';
import db from '../../db/index.js';
import { socialSource } from '../../db/schema/activities.js';
import { ilike, sql } from 'drizzle-orm';
import z from 'zod';

const router: Router = Router();

const QuerySchema = z.object({
    limit: z.coerce.number().min(1).max(100).default(10),
    offset: z.coerce.number().min(0).default(0),
    search: z.string().optional()
});

router.get('/', async (request, response) => {
    try {
        const { limit, offset, search } = QuerySchema.parse(request.query);

        // Build the where condition
        const whereCondition = search
            ? ilike(socialSource.url, `%${search}%`)
            : undefined;

        // Get total count for pagination info
        const countResult = await db
            .select({
                count: sql<number>`count(*)`
            })
            .from(socialSource)
            .where(whereCondition);

        const totalCount = countResult[0]?.count || 0;

        // Execute the main query with pagination
        const sources = await db
            .select({
                url: socialSource.url,
                name: socialSource.name,
                sourceKey: socialSource.sourceKey,
                logo: socialSource.logo
            })
            .from(socialSource)
            .where(whereCondition)
            .limit(limit)
            .offset(offset)
            .orderBy(socialSource.name);

        return response.status(200).json({
            sources,
            pagination: {
                total: totalCount,
                limit,
                offset
            }
        });
    } catch (error) {
        console.error('Error fetching social sources:', error);
        return response.status(500).json({ error: 'Failed to fetch social sources' });
    }
});

export default router;
