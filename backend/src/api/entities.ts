import { Router } from 'express';
import z, { ZodError } from 'zod';

import { log } from '@quarterback/util/gcp';
import { ListedEntity } from '@quarterback/types';
import { sql, eq, and } from 'drizzle-orm';
import db from '../db/index.js';
import { listedEntity } from '../db/schema/market.js';
import AuthenticatedRequest from '../auth/AuthenticatedRequest.js';
import verifyUserOrganisation from '../auth/verifyUserOrganisation.js';

const router: Router = Router();

const UpdateEntitySchema = ListedEntity.extend({
    organisation: z.string()
});

type UpdateEntitySchema = z.infer<typeof UpdateEntitySchema>;

router.put('/', async (request: AuthenticatedRequest, response) => {
    try {
        const entityData = UpdateEntitySchema.parse(request.body);
        const userId = request.user!.sub;

        const hasAccess = await verifyUserOrganisation(
            userId,
            entityData.organisation,
            entityData.symbol,
            entityData.exchange
        );

        if (!hasAccess) {
            return response.status(403).json('Access denied to this entity');
        }

        const existingEntity = await db
            .select()
            .from(listedEntity)
            .where(
                and(
                    eq(listedEntity.symbol, entityData.symbol),
                    eq(listedEntity.exchange, entityData.exchange)
                )
            );

        if (existingEntity.length === 0) {
            return response
                .status(404)
                .json('Entity not found. Only existing entities can be updated.');
        }

        const updated = await db
            .update(listedEntity)
            .set({
                name: entityData.name,
                twitterUsername: entityData.twitterUsername || '',
                twitterQuery: entityData.twitterQuery || '',
                newsQuery: entityData.newsQuery || '',
                redditQuery: entityData.redditQuery || '',
                linkedInQuery: entityData.linkedInQuery || '',
                linkedInUsername: entityData.linkedInUsername || '',
                about: entityData.about
            })
            .where(
                and(
                    eq(listedEntity.symbol, entityData.symbol),
                    eq(listedEntity.exchange, entityData.exchange)
                )
            )
            .returning();

        return response.status(200).json(updated[0]);
    } catch (error) {
        if (error instanceof ZodError) {
            log('INFO', `Bad request ${error}`, { body: request.body });
            return response.status(400).json('Bad request');
        } else {
            log('ERROR', `Error updating entity: ${error}`);
            return response.status(500).json('Internal server error');
        }
    }
});

export default router;
