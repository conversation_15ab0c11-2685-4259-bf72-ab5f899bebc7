{"id": "1a8cef70-6b4e-4cdd-9b98-00e876473f8b", "prevId": "7d30740a-3cca-40a3-8992-4b101f910cbe", "version": "7", "dialect": "postgresql", "tables": {"public.Activity": {"name": "Activity", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "posted": {"name": "posted", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "format": {"name": "format", "type": "format", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Activity_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "Activity_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "Activity", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Activity_key_symbol_exchange_unique": {"name": "Activity_key_symbol_exchange_unique", "nullsNotDistinct": false, "columns": ["key", "symbol", "exchange"]}}}, "public.ActivityFiles": {"name": "ActivityFiles", "schema": "", "columns": {"storagePath": {"name": "storagePath", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": true, "notNull": true}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "fileName": {"name": "fileName", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "fileSize": {"name": "fileSize", "type": "integer", "primaryKey": false, "notNull": true}, "fileType": {"name": "fileType", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "uploadedAt": {"name": "uploadedAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ActivityFiles_activity_idx": {"name": "ActivityFiles_activity_idx", "columns": [{"expression": "activity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ActivityFiles_activity_Activity_id_fk": {"name": "ActivityFiles_activity_Activity_id_fk", "tableFrom": "ActivityFiles", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ADVFNPost": {"name": "ADVFNPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"ADVFNPost_activity_Activity_id_fk": {"name": "ADVFNPost_activity_Activity_id_fk", "tableFrom": "ADVFNPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ADVFNPost_user_Author_key_fk": {"name": "ADVFNPost_user_Author_key_fk", "tableFrom": "ADVFNPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ApplePodcastPost": {"name": "ApplePodcastPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"ApplePodcastPost_activity_Activity_id_fk": {"name": "ApplePodcastPost_activity_Activity_id_fk", "tableFrom": "ApplePodcastPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ApplePodcastPost_user_Author_key_fk": {"name": "ApplePodcastPost_user_Author_key_fk", "tableFrom": "ApplePodcastPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivityArchived": {"name": "ActivityArchived", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ActivityArchived_activity_Activity_id_fk": {"name": "ActivityArchived_activity_Activity_id_fk", "tableFrom": "ActivityArchived", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ActivityArchived_organisation_Organisation_id_fk": {"name": "ActivityArchived_organisation_Organisation_id_fk", "tableFrom": "ActivityArchived", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"ActivityArchived_activity_organisation_pk": {"name": "ActivityArchived_activity_organisation_pk", "columns": ["activity", "organisation"]}}, "uniqueConstraints": {}}, "public.ASXAnnouncement": {"name": "ASXAnnouncement", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "priceSensitive": {"name": "priceSensitive", "type": "boolean", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"ASXAnnouncement_activity_Activity_id_fk": {"name": "ASXAnnouncement_activity_Activity_id_fk", "tableFrom": "ASXAnnouncement", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AudiblePost": {"name": "AudiblePost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"AudiblePost_activity_Activity_id_fk": {"name": "AudiblePost_activity_Activity_id_fk", "tableFrom": "AudiblePost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "AudiblePost_user_Author_key_fk": {"name": "AudiblePost_user_Author_key_fk", "tableFrom": "AudiblePost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AussieStockForums": {"name": "AussieStockForums", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"AussieStockForums_activity_Activity_id_fk": {"name": "AussieStockForums_activity_Activity_id_fk", "tableFrom": "AussieStockForums", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "AussieStockForums_user_Author_key_fk": {"name": "AussieStockForums_user_Author_key_fk", "tableFrom": "AussieStockForums", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Author": {"name": "Author", "schema": "", "columns": {"key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": false}, "following": {"name": "following", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.BogleHeads": {"name": "BogleHeads", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"BogleHeads_activity_Activity_id_fk": {"name": "BogleHeads_activity_Activity_id_fk", "tableFrom": "BogleHeads", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "BogleHeads_user_Author_key_fk": {"name": "BogleHeads_user_Author_key_fk", "tableFrom": "BogleHeads", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.CastboxPost": {"name": "CastboxPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"CastboxPost_activity_Activity_id_fk": {"name": "CastboxPost_activity_Activity_id_fk", "tableFrom": "CastboxPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "CastboxPost_user_Author_key_fk": {"name": "CastboxPost_user_Author_key_fk", "tableFrom": "CastboxPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ClubhousePost": {"name": "ClubhousePost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"ClubhousePost_activity_Activity_id_fk": {"name": "ClubhousePost_activity_Activity_id_fk", "tableFrom": "ClubhousePost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ClubhousePost_user_Author_key_fk": {"name": "ClubhousePost_user_Author_key_fk", "tableFrom": "ClubhousePost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DiscordPost": {"name": "DiscordPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"DiscordPost_activity_Activity_id_fk": {"name": "DiscordPost_activity_Activity_id_fk", "tableFrom": "DiscordPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "DiscordPost_user_Author_key_fk": {"name": "DiscordPost_user_Author_key_fk", "tableFrom": "DiscordPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.FacebookPost": {"name": "FacebookPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"FacebookPost_activity_Activity_id_fk": {"name": "FacebookPost_activity_Activity_id_fk", "tableFrom": "FacebookPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "FacebookPost_user_Author_key_fk": {"name": "FacebookPost_user_Author_key_fk", "tableFrom": "FacebookPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivityFlagged": {"name": "ActivityFlagged", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ActivityFlagged_activity_Activity_id_fk": {"name": "ActivityFlagged_activity_Activity_id_fk", "tableFrom": "ActivityFlagged", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ActivityFlagged_organisation_Organisation_id_fk": {"name": "ActivityFlagged_organisation_Organisation_id_fk", "tableFrom": "ActivityFlagged", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"ActivityFlagged_activity_organisation_pk": {"name": "ActivityFlagged_activity_organisation_pk", "columns": ["activity", "organisation"]}}, "uniqueConstraints": {}}, "public.HotCopperPost": {"name": "HotCopperPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "post": {"name": "post", "type": "integer", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true}, "sentiment": {"name": "sentiment", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "disclosure": {"name": "disclosure", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"HotCopperPost_thread_HotCopperThread_thread_fk": {"name": "HotCopperPost_thread_HotCopperThread_thread_fk", "tableFrom": "HotCopperPost", "tableTo": "HotCopperThread", "columnsFrom": ["thread"], "columnsTo": ["thread"], "onDelete": "no action", "onUpdate": "no action"}, "HotCopperPost_activity_Activity_id_fk": {"name": "HotCopperPost_activity_Activity_id_fk", "tableFrom": "HotCopperPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "HotCopperPost_user_Author_key_fk": {"name": "HotCopperPost_user_Author_key_fk", "tableFrom": "HotCopperPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"HotCopperPost_post_unique": {"name": "HotCopperPost_post_unique", "nullsNotDistinct": false, "columns": ["post"]}}}, "public.HotCopperThread": {"name": "HotCopperThread", "schema": "", "columns": {"thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.iHeartRadioPost": {"name": "iHeartRadioPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"iHeartRadioPost_activity_Activity_id_fk": {"name": "iHeartRadioPost_activity_Activity_id_fk", "tableFrom": "iHeartRadioPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "iHeartRadioPost_user_Author_key_fk": {"name": "iHeartRadioPost_user_Author_key_fk", "tableFrom": "iHeartRadioPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.InstagramPost": {"name": "InstagramPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"InstagramPost_activity_Activity_id_fk": {"name": "InstagramPost_activity_Activity_id_fk", "tableFrom": "InstagramPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "InstagramPost_user_Author_key_fk": {"name": "InstagramPost_user_Author_key_fk", "tableFrom": "InstagramPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.InvestorHubPost": {"name": "InvestorHubPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"InvestorHubPost_activity_Activity_id_fk": {"name": "InvestorHubPost_activity_Activity_id_fk", "tableFrom": "InvestorHubPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "InvestorHubPost_user_Author_key_fk": {"name": "InvestorHubPost_user_Author_key_fk", "tableFrom": "InvestorHubPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.LinkedInCookies": {"name": "LinkedInCookies", "schema": "", "columns": {"email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": true, "notNull": true}, "cookie": {"name": "cookie", "type": "json", "primaryKey": false, "notNull": true}, "lastUsed": {"name": "lastUsed", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.LinkedInPost": {"name": "LinkedInPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "commentUrn": {"name": "commentUrn", "type": "text", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"LinkedInPost_activity_Activity_id_fk": {"name": "LinkedInPost_activity_Activity_id_fk", "tableFrom": "LinkedInPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "LinkedInPost_user_Author_key_fk": {"name": "LinkedInPost_user_Author_key_fk", "tableFrom": "LinkedInPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.MediumPost": {"name": "MediumPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"MediumPost_activity_Activity_id_fk": {"name": "MediumPost_activity_Activity_id_fk", "tableFrom": "MediumPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "MediumPost_user_Author_key_fk": {"name": "MediumPost_user_Author_key_fk", "tableFrom": "MediumPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.NewsArticle": {"name": "NewsArticle", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"NewsArticle_source_NewsSource_url_fk": {"name": "NewsArticle_source_NewsSource_url_fk", "tableFrom": "NewsArticle", "tableTo": "NewsSource", "columnsFrom": ["source"], "columnsTo": ["url"], "onDelete": "no action", "onUpdate": "no action"}, "NewsArticle_activity_Activity_id_fk": {"name": "NewsArticle_activity_Activity_id_fk", "tableFrom": "NewsArticle", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.NewsSource": {"name": "NewsSource", "schema": "", "columns": {"url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.PinterestPost": {"name": "PinterestPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"PinterestPost_activity_Activity_id_fk": {"name": "PinterestPost_activity_Activity_id_fk", "tableFrom": "PinterestPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "PinterestPost_user_Author_key_fk": {"name": "PinterestPost_user_Author_key_fk", "tableFrom": "PinterestPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.QuoraPost": {"name": "QuoraPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"QuoraPost_activity_Activity_id_fk": {"name": "QuoraPost_activity_Activity_id_fk", "tableFrom": "QuoraPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "QuoraPost_user_Author_key_fk": {"name": "Quora<PERSON>ost_user_Author_key_fk", "tableFrom": "QuoraPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivityRead": {"name": "ActivityRead", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ActivityRead_activity_Activity_id_fk": {"name": "ActivityRead_activity_Activity_id_fk", "tableFrom": "ActivityRead", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"ActivityRead_activity_user_pk": {"name": "ActivityRead_activity_user_pk", "columns": ["activity", "user"]}}, "uniqueConstraints": {}}, "public.RedditComment": {"name": "RedditComment", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "parent": {"name": "parent", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "post": {"name": "post", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"RedditComment_activity_Activity_id_fk": {"name": "RedditComment_activity_Activity_id_fk", "tableFrom": "RedditComment", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "RedditComment_user_Author_key_fk": {"name": "RedditComment_user_Author_key_fk", "tableFrom": "RedditComment", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.RedditPost": {"name": "RedditPost", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "ratio": {"name": "ratio", "type": "double precision", "primaryKey": false, "notNull": true}, "subreddit": {"name": "subreddit", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"RedditPost_activity_Activity_id_fk": {"name": "RedditPost_activity_Activity_id_fk", "tableFrom": "RedditPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "RedditPost_user_Author_key_fk": {"name": "RedditPost_user_Author_key_fk", "tableFrom": "RedditPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivitySentiment": {"name": "ActivitySentiment", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "magnitude": {"name": "magnitude", "type": "double precision", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "double precision", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"ActivitySentiment_activity_Activity_id_fk": {"name": "ActivitySentiment_activity_Activity_id_fk", "tableFrom": "ActivitySentiment", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.SlackPost": {"name": "SlackPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"SlackPost_activity_Activity_id_fk": {"name": "SlackPost_activity_Activity_id_fk", "tableFrom": "SlackPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "SlackPost_user_Author_key_fk": {"name": "SlackPost_user_Author_key_fk", "tableFrom": "SlackPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.SnapchatPost": {"name": "SnapchatPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"SnapchatPost_activity_Activity_id_fk": {"name": "SnapchatPost_activity_Activity_id_fk", "tableFrom": "SnapchatPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "SnapchatPost_user_Author_key_fk": {"name": "SnapchatPost_user_Author_key_fk", "tableFrom": "SnapchatPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.SocialSource": {"name": "SocialSource", "schema": "", "columns": {"url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.SpotifyPost": {"name": "SpotifyPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"SpotifyPost_activity_Activity_id_fk": {"name": "SpotifyPost_activity_Activity_id_fk", "tableFrom": "SpotifyPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "SpotifyPost_user_Author_key_fk": {"name": "SpotifyPost_user_Author_key_fk", "tableFrom": "SpotifyPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.StocktwitsPost": {"name": "StocktwitsPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"StocktwitsPost_activity_Activity_id_fk": {"name": "StocktwitsPost_activity_Activity_id_fk", "tableFrom": "StocktwitsPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "StocktwitsPost_user_Author_key_fk": {"name": "StocktwitsPost_user_Author_key_fk", "tableFrom": "StocktwitsPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.StrawmanPost": {"name": "StrawmanPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"StrawmanPost_activity_Activity_id_fk": {"name": "StrawmanPost_activity_Activity_id_fk", "tableFrom": "StrawmanPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "StrawmanPost_user_Author_key_fk": {"name": "StrawmanPost_user_Author_key_fk", "tableFrom": "StrawmanPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivitySummary": {"name": "ActivitySummary", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ActivitySummary_activity_Activity_id_fk": {"name": "ActivitySummary_activity_Activity_id_fk", "tableFrom": "ActivitySummary", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TelegramPost": {"name": "TelegramPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"TelegramPost_activity_Activity_id_fk": {"name": "TelegramPost_activity_Activity_id_fk", "tableFrom": "TelegramPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TelegramPost_user_Author_key_fk": {"name": "TelegramPost_user_Author_key_fk", "tableFrom": "TelegramPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TikTokPost": {"name": "TikTokPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"TikTokPost_activity_Activity_id_fk": {"name": "TikTokPost_activity_Activity_id_fk", "tableFrom": "TikTokPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TikTokPost_user_Author_key_fk": {"name": "Tik<PERSON>okPost_user_Author_key_fk", "tableFrom": "TikTokPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TradingQnAPost": {"name": "TradingQnAPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"TradingQnAPost_activity_Activity_id_fk": {"name": "TradingQnAPost_activity_Activity_id_fk", "tableFrom": "TradingQnAPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TradingQnAPost_user_Author_key_fk": {"name": "TradingQnAPost_user_Author_key_fk", "tableFrom": "TradingQnAPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TumblrPost": {"name": "TumblrPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"TumblrPost_activity_Activity_id_fk": {"name": "TumblrPost_activity_Activity_id_fk", "tableFrom": "TumblrPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TumblrPost_user_Author_key_fk": {"name": "TumblrPost_user_Author_key_fk", "tableFrom": "TumblrPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TwitterPost": {"name": "TwitterPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"thread_idx": {"name": "thread_idx", "columns": [{"expression": "thread", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TwitterPost_activity_Activity_id_fk": {"name": "TwitterPost_activity_Activity_id_fk", "tableFrom": "TwitterPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TwitterPost_user_Author_key_fk": {"name": "TwitterPost_user_Author_key_fk", "tableFrom": "TwitterPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.VimeoPost": {"name": "VimeoPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"VimeoPost_activity_Activity_id_fk": {"name": "VimeoPost_activity_Activity_id_fk", "tableFrom": "VimeoPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "VimeoPost_user_Author_key_fk": {"name": "VimeoPost_user_Author_key_fk", "tableFrom": "VimeoPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.WeChatPost": {"name": "WeChatPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"WeChatPost_activity_Activity_id_fk": {"name": "WeChatPost_activity_Activity_id_fk", "tableFrom": "WeChatPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "WeChatPost_user_Author_key_fk": {"name": "WeChatPost_user_Author_key_fk", "tableFrom": "WeChatPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.WhatsAppPost": {"name": "WhatsAppPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"WhatsAppPost_activity_Activity_id_fk": {"name": "WhatsAppPost_activity_Activity_id_fk", "tableFrom": "WhatsAppPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "WhatsAppPost_user_Author_key_fk": {"name": "WhatsAppPost_user_Author_key_fk", "tableFrom": "WhatsAppPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.WhirlpoolFinancePost": {"name": "WhirlpoolFinancePost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"WhirlpoolFinancePost_activity_Activity_id_fk": {"name": "WhirlpoolFinancePost_activity_Activity_id_fk", "tableFrom": "WhirlpoolFinancePost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "WhirlpoolFinancePost_user_Author_key_fk": {"name": "WhirlpoolFinancePost_user_Author_key_fk", "tableFrom": "WhirlpoolFinancePost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.YouTubePost": {"name": "YouTubePost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "isBroadcast": {"name": "isBroadcast", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"YouTubePost_activity_Activity_id_fk": {"name": "YouTubePost_activity_Activity_id_fk", "tableFrom": "YouTubePost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "YouTubePost_user_Author_key_fk": {"name": "YouTubePost_user_Author_key_fk", "tableFrom": "YouTubePost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Insight": {"name": "Insight", "schema": "", "columns": {"hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Insight_organisation_Organisation_id_fk": {"name": "Insight_organisation_Organisation_id_fk", "tableFrom": "Insight", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Insight_hash_organisation_pk": {"name": "Insight_hash_organisation_pk", "columns": ["hash", "organisation"]}}, "uniqueConstraints": {}}, "public.AlertConfig": {"name": "Alert<PERSON>onfig", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "field": {"name": "field", "type": "field", "typeSchema": "public", "primaryKey": false, "notNull": true}, "comparator": {"name": "comparator", "type": "comparator", "typeSchema": "public", "primaryKey": false, "notNull": true}, "threshold": {"name": "threshold", "type": "double precision", "primaryKey": false, "notNull": true}, "interval": {"name": "interval", "type": "intervalEnum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "searchTerm": {"name": "searchTerm", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"AlertConfig_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "AlertConfig_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "Alert<PERSON>onfig", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "cascade", "onUpdate": "no action"}, "AlertConfig_organisation_Organisation_id_fk": {"name": "AlertConfig_organisation_Organisation_id_fk", "tableFrom": "Alert<PERSON>onfig", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AlertEmail": {"name": "AlertEmail", "schema": "", "columns": {"alert": {"name": "alert", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"AlertEmail_alert_email_pk": {"name": "AlertEmail_alert_email_pk", "columns": ["alert", "email"]}}, "uniqueConstraints": {}}, "public.AlertNotification": {"name": "AlertNotification", "schema": "", "columns": {"alert": {"name": "alert", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"AlertNotification_alert_AlertConfig_id_fk": {"name": "AlertNotification_alert_AlertConfig_id_fk", "tableFrom": "AlertNotification", "tableTo": "Alert<PERSON>onfig", "columnsFrom": ["alert"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"AlertNotification_alert_at_pk": {"name": "AlertNotification_alert_at_pk", "columns": ["alert", "at"]}}, "uniqueConstraints": {}}, "public.ListedEntity": {"name": "ListedEntity", "schema": "", "columns": {"symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "twitterUsername": {"name": "twitterUsername", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "default": "''"}, "twitterQuery": {"name": "twitterQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "newsQuery": {"name": "newsQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "redditQuery": {"name": "redditQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "linkedInQuery": {"name": "linkedInQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "linkedInUsername": {"name": "linkedInUsername", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "linkedInCompanyId": {"name": "linkedInCompanyId", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "about": {"name": "about", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ListedEntity_symbol_exchange_pk": {"name": "ListedEntity_symbol_exchange_pk", "columns": ["symbol", "exchange"]}}, "uniqueConstraints": {}}, "public.Organisation": {"name": "Organisation", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.OrganisationUserEntity": {"name": "OrganisationUserEntity", "schema": "", "columns": {"user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"OrganisationUserEntity_organisation_Organisation_id_fk": {"name": "OrganisationUserEntity_organisation_Organisation_id_fk", "tableFrom": "OrganisationUserEntity", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "OrganisationUserEntity_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "OrganisationUserEntity_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "OrganisationUserEntity", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"OrganisationUserEntity_user_organisation_symbol_exchange_pk": {"name": "OrganisationUserEntity_user_organisation_symbol_exchange_pk", "columns": ["user", "organisation", "symbol", "exchange"]}}, "uniqueConstraints": {}}, "public.LinkedInProfileSnapshot": {"name": "LinkedInProfileSnapshot", "schema": "", "columns": {"at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"LinkedInProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "LinkedInProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "LinkedInProfileSnapshot", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"LinkedInProfileSnapshot_at_username_pk": {"name": "LinkedInProfileSnapshot_at_username_pk", "columns": ["at", "username"]}}, "uniqueConstraints": {}}, "public.MailchimpConfiguration": {"name": "MailchimpConfiguration", "schema": "", "columns": {"token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "audience": {"name": "audience", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"MailchimpConfiguration_organisation_Organisation_id_fk": {"name": "MailchimpConfiguration_organisation_Organisation_id_fk", "tableFrom": "MailchimpConfiguration", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "MailchimpConfiguration_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "MailchimpConfiguration_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "MailchimpConfiguration", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"MailchimpConfiguration_organisation_symbol_exchange_pk": {"name": "MailchimpConfiguration_organisation_symbol_exchange_pk", "columns": ["organisation", "symbol", "exchange"]}}, "uniqueConstraints": {}}, "public.MailchimpSubscriberSnapshot": {"name": "MailchimpSubscriberSnapshot", "schema": "", "columns": {"at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "audience": {"name": "audience", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "subscribers": {"name": "subscribers", "type": "integer", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"MailchimpSubscriberSnapshot_organisation_Organisation_id_fk": {"name": "MailchimpSubscriberSnapshot_organisation_Organisation_id_fk", "tableFrom": "MailchimpSubscriberSnapshot", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "MailchimpSubscriberSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "MailchimpSubscriberSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "MailchimpSubscriberSnapshot", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"MailchimpSubscriberSnapshot_organisation_symbol_exchange_audience_at_pk": {"name": "MailchimpSubscriberSnapshot_organisation_symbol_exchange_audience_at_pk", "columns": ["organisation", "symbol", "exchange", "audience", "at"]}}, "uniqueConstraints": {}}, "public.TwitterProfileSnapshot": {"name": "TwitterProfileSnapshot", "schema": "", "columns": {"at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"TwitterProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "TwitterProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "TwitterProfileSnapshot", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"TwitterProfileSnapshot_at_username_pk": {"name": "TwitterProfileSnapshot_at_username_pk", "columns": ["at", "username"]}}, "uniqueConstraints": {}}}, "enums": {"public.format": {"name": "format", "schema": "public", "values": ["chatter", "broadcast", "media", "announcement", "call", "meeting", "presentation", "event", "other"]}, "public.comparator": {"name": "comparator", "schema": "public", "values": ["GTE", "LTE"]}, "public.field": {"name": "field", "schema": "public", "values": ["SENTIMENT", "SHARE_PERCENT", "ACTIVITY", "SEARCH"]}, "public.intervalEnum": {"name": "intervalEnum", "schema": "public", "values": ["DAY", "WEEK", "MONTH"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}