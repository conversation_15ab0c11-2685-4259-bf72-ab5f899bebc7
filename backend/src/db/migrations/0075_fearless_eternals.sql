ALTER TABLE "ADVFNPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "ApplePodcastPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "AudiblePost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "AussieStockForums" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "BogleHeads" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "CallPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "CastboxPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "ClubhousePost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "DiscordPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "EventPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "FacebookPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "HotCopperPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "iHeartRadioPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "InstagramPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "InvestorHubPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "LinkedInPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "MediumPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "MeetingPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "OtherPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "PinterestPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "PresentationPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "QuoraPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "RedditPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "SlackPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "SnapchatPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "SpotifyPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "StocktwitsPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "StrawmanPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "TelegramPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "TikTokPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "TradingQnAPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "TumblrPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "TwitterPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "VimeoPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "WeChatPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "WhatsAppPost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "WhirlpoolFinancePost" DROP COLUMN IF EXISTS "isBroadcast";--> statement-breakpoint
ALTER TABLE "YouTubePost" DROP COLUMN IF EXISTS "isBroadcast";