

-- Migration to move isBroadcast data to Activity.format column

-- Step 1: Update Activity.format based on ASXAnnouncement records
UPDATE "Activity"
SET "format" = 'announcement'::format
WHERE "id" IN (
    SELECT "activity" FROM "ASXAnnouncement"
);

-- Step 2: Update Activity.format based on NewsArticle records
UPDATE "Activity"
SET "format" = 'media'::format
WHERE "id" IN (
    SELECT "activity" FROM "NewsArticle"
);

-- Step 3: Update Activity.format based on isBroadcast values for HotCopperPost
UPDATE "Activity"
SET "format" = CASE
    WHEN hcp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "HotCopperPost" hcp
WHERE "Activity"."id" = hcp."activity"
AND "Activity"."format" IS NULL;

-- Step 4: Update Activity.format based on isBroadcast values for TwitterPost
UPDATE "Activity"
SET "format" = CASE
    WHEN tp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "TwitterPost" tp
WHERE "Activity"."id" = tp."activity"
AND "Activity"."format" IS NULL;

-- Step 5: Update Activity.format based on isBroadcast values for RedditPost
UPDATE "Activity"
SET "format" = CASE
    WHEN rp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "RedditPost" rp
WHERE "Activity"."id" = rp."activity"
AND "Activity"."format" IS NULL;

-- Step 6: Update Activity.format based on isBroadcast values for LinkedInPost
UPDATE "Activity"
SET "format" = CASE
    WHEN lp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "LinkedInPost" lp
WHERE "Activity"."id" = lp."activity"
AND "Activity"."format" IS NULL;

-- Step 7: Update Activity.format based on isBroadcast values for ADVFNPost
UPDATE "Activity"
SET "format" = CASE
    WHEN ap."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "ADVFNPost" ap
WHERE "Activity"."id" = ap."activity"
AND "Activity"."format" IS NULL;

-- Step 8: Update Activity.format based on isBroadcast values for ApplePodcastPost
UPDATE "Activity"
SET "format" = CASE
    WHEN app."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "ApplePodcastPost" app
WHERE "Activity"."id" = app."activity"
AND "Activity"."format" IS NULL;

-- Step 9: Update Activity.format based on isBroadcast values for AudiblePost
UPDATE "Activity"
SET "format" = CASE
    WHEN aup."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "AudiblePost" aup
WHERE "Activity"."id" = aup."activity"
AND "Activity"."format" IS NULL;

-- Step 10: Update Activity.format based on isBroadcast values for AussieStockForums
UPDATE "Activity"
SET "format" = CASE
    WHEN asf."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "AussieStockForums" asf
WHERE "Activity"."id" = asf."activity"
AND "Activity"."format" IS NULL;

-- Step 11: Update Activity.format based on isBroadcast values for CastboxPost
UPDATE "Activity"
SET "format" = CASE
    WHEN cp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "CastboxPost" cp
WHERE "Activity"."id" = cp."activity"
AND "Activity"."format" IS NULL;

-- Step 12: Update Activity.format based on isBroadcast values for ClubhousePost
UPDATE "Activity"
SET "format" = CASE
    WHEN chp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "ClubhousePost" chp
WHERE "Activity"."id" = chp."activity"
AND "Activity"."format" IS NULL;

-- Step 13: Update Activity.format based on isBroadcast values for DiscordPost
UPDATE "Activity"
SET "format" = CASE
    WHEN dp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "DiscordPost" dp
WHERE "Activity"."id" = dp."activity"
AND "Activity"."format" IS NULL;

-- Step 14: Update Activity.format based on isBroadcast values for FacebookPost
UPDATE "Activity"
SET "format" = CASE
    WHEN fp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "FacebookPost" fp
WHERE "Activity"."id" = fp."activity"
AND "Activity"."format" IS NULL;

-- Step 15: Update Activity.format based on isBroadcast values for iHeartRadioPost
UPDATE "Activity"
SET "format" = CASE
    WHEN ihrp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "iHeartRadioPost" ihrp
WHERE "Activity"."id" = ihrp."activity"
AND "Activity"."format" IS NULL;

-- Step 16: Update Activity.format based on isBroadcast values for InstagramPost
UPDATE "Activity"
SET "format" = CASE
    WHEN ip."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "InstagramPost" ip
WHERE "Activity"."id" = ip."activity"
AND "Activity"."format" IS NULL;

-- Step 17: Update Activity.format based on isBroadcast values for InvestorHubPost
UPDATE "Activity"
SET "format" = CASE
    WHEN ihp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "InvestorHubPost" ihp
WHERE "Activity"."id" = ihp."activity"
AND "Activity"."format" IS NULL;

-- Step 18: Update Activity.format based on isBroadcast values for MediumPost
UPDATE "Activity"
SET "format" = CASE
    WHEN mp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "MediumPost" mp
WHERE "Activity"."id" = mp."activity"
AND "Activity"."format" IS NULL;

-- Step 19: Update Activity.format based on isBroadcast values for PinterestPost
UPDATE "Activity"
SET "format" = CASE
    WHEN pp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "PinterestPost" pp
WHERE "Activity"."id" = pp."activity"
AND "Activity"."format" IS NULL;

-- Step 20: Update Activity.format based on isBroadcast values for QuoraPost
UPDATE "Activity"
SET "format" = CASE
    WHEN qp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "QuoraPost" qp
WHERE "Activity"."id" = qp."activity"
AND "Activity"."format" IS NULL;

-- Step 21: Update Activity.format based on isBroadcast values for SlackPost
UPDATE "Activity"
SET "format" = CASE
    WHEN sp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "SlackPost" sp
WHERE "Activity"."id" = sp."activity"
AND "Activity"."format" IS NULL;

-- Step 22: Update Activity.format based on isBroadcast values for SnapchatPost
UPDATE "Activity"
SET "format" = CASE
    WHEN scp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "SnapchatPost" scp
WHERE "Activity"."id" = scp."activity"
AND "Activity"."format" IS NULL;

-- Step 23: Update Activity.format based on isBroadcast values for SpotifyPost
UPDATE "Activity"
SET "format" = CASE
    WHEN spp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "SpotifyPost" spp
WHERE "Activity"."id" = spp."activity"
AND "Activity"."format" IS NULL;

-- Step 24: Update Activity.format based on isBroadcast values for StocktwitsPost
UPDATE "Activity"
SET "format" = CASE
    WHEN stp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "StocktwitsPost" stp
WHERE "Activity"."id" = stp."activity"
AND "Activity"."format" IS NULL;

-- Step 25: Update Activity.format based on isBroadcast values for StrawmanPost
UPDATE "Activity"
SET "format" = CASE
    WHEN strp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "StrawmanPost" strp
WHERE "Activity"."id" = strp."activity"
AND "Activity"."format" IS NULL;

-- Step 26: Update Activity.format based on isBroadcast values for TelegramPost
UPDATE "Activity"
SET "format" = CASE
    WHEN tep."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "TelegramPost" tep
WHERE "Activity"."id" = tep."activity"
AND "Activity"."format" IS NULL;

-- Step 27: Update Activity.format based on isBroadcast values for TikTokPost
UPDATE "Activity"
SET "format" = CASE
    WHEN ttp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "TikTokPost" ttp
WHERE "Activity"."id" = ttp."activity"
AND "Activity"."format" IS NULL;

-- Step 28: Update Activity.format based on isBroadcast values for TradingQnAPost
UPDATE "Activity"
SET "format" = CASE
    WHEN tqp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "TradingQnAPost" tqp
WHERE "Activity"."id" = tqp."activity"
AND "Activity"."format" IS NULL;

-- Step 29: Update Activity.format based on isBroadcast values for TumblrPost
UPDATE "Activity"
SET "format" = CASE
    WHEN tup."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "TumblrPost" tup
WHERE "Activity"."id" = tup."activity"
AND "Activity"."format" IS NULL;

-- Step 30: Update Activity.format based on isBroadcast values for VimeoPost
UPDATE "Activity"
SET "format" = CASE
    WHEN vp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "VimeoPost" vp
WHERE "Activity"."id" = vp."activity"
AND "Activity"."format" IS NULL;

-- Step 31: Update Activity.format based on isBroadcast values for WeChatPost
UPDATE "Activity"
SET "format" = CASE
    WHEN wcp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "WeChatPost" wcp
WHERE "Activity"."id" = wcp."activity"
AND "Activity"."format" IS NULL;

-- Step 32: Update Activity.format based on isBroadcast values for WhatsAppPost
UPDATE "Activity"
SET "format" = CASE
    WHEN wap."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "WhatsAppPost" wap
WHERE "Activity"."id" = wap."activity"
AND "Activity"."format" IS NULL;

-- Step 33: Update Activity.format based on isBroadcast values for WhirlpoolFinancePost
UPDATE "Activity"
SET "format" = CASE
    WHEN wfp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "WhirlpoolFinancePost" wfp
WHERE "Activity"."id" = wfp."activity"
AND "Activity"."format" IS NULL;

-- Step 34: Update Activity.format based on isBroadcast values for YouTubePost
UPDATE "Activity"
SET "format" = CASE
    WHEN yp."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "YouTubePost" yp
WHERE "Activity"."id" = yp."activity"
AND "Activity"."format" IS NULL;

-- Step 35: Update Activity.format based on isBroadcast values for BogleHeads
UPDATE "Activity"
SET "format" = CASE
    WHEN bh."isBroadcast" = true THEN 'broadcast'::format
    ELSE 'chatter'::format
END
FROM "BogleHeads" bh
WHERE "Activity"."id" = bh."activity"
AND "Activity"."format" IS NULL;

-- Step 36: Update Activity.format for CallPost (set to 'call' format)
UPDATE "Activity"
SET "format" = 'call'::format
WHERE "id" IN (
    SELECT "activity" FROM "CallPost"
)
AND "Activity"."format" IS NULL;

-- Step 37: Update Activity.format for MeetingPost (set to 'meeting' format)
UPDATE "Activity"
SET "format" = 'meeting'::format
WHERE "id" IN (
    SELECT "activity" FROM "MeetingPost"
)
AND "Activity"."format" IS NULL;

-- Step 38: Update Activity.format for EventPost (set to 'event' format)
UPDATE "Activity"
SET "format" = 'event'::format
WHERE "id" IN (
    SELECT "activity" FROM "EventPost"
)
AND "Activity"."format" IS NULL;

-- Step 39: Update Activity.format for PresentationPost (set to 'presentation' format)
UPDATE "Activity"
SET "format" = 'presentation'::format
WHERE "id" IN (
    SELECT "activity" FROM "PresentationPost"
)
AND "Activity"."format" IS NULL;

-- Step 40: Update Activity.format for OtherPost (set to 'other' format)
UPDATE "Activity"
SET "format" = 'other'::format
WHERE "id" IN (
    SELECT "activity" FROM "OtherPost"
)
AND "Activity"."format" IS NULL;