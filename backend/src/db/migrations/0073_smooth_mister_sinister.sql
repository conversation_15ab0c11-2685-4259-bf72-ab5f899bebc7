CREATE TABLE IF NOT EXISTS "CallPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "EventPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"title" varchar(256),
	"body" text NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "MeetingPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"title" varchar(256),
	"body" text NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "OtherPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"title" varchar(256),
	"body" text NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "PresentationPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"title" varchar(256),
	"body" text NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CallPost" ADD CONSTRAINT "CallPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CallPost" ADD CONSTRAINT "CallPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "EventPost" ADD CONSTRAINT "EventPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "EventPost" ADD CONSTRAINT "EventPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MeetingPost" ADD CONSTRAINT "MeetingPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MeetingPost" ADD CONSTRAINT "MeetingPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OtherPost" ADD CONSTRAINT "OtherPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OtherPost" ADD CONSTRAINT "OtherPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PresentationPost" ADD CONSTRAINT "PresentationPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PresentationPost" ADD CONSTRAINT "PresentationPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
