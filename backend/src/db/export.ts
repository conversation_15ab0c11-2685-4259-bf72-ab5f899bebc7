import * as activitiesRepository from './repositories/activities.js';
import db from './index.js';
import { Activity, Sentiment } from '@quarterback/types';
import { download, generateCsv, mkConfig } from 'export-to-csv';
import fs from 'node:fs';

const SYMBOL = 'TM1';

function scaledMagnitude(magnitude: number): number {
    return 4 / (1 + Math.exp(-0.3 * magnitude)) - 2;
}

export function sentimentScore({ score, magnitude }: Sentiment): number {
    return Math.max(Math.min(score * scaledMagnitude(magnitude), 1), -1);
}

const from = new Date('2024-07-01T00:00:00.000Z');
const to = new Date('2024-08-01T00:00:00.000Z');

const activities = await activitiesRepository.read(db, '', '', SYMBOL, 'ASX', {
    from,
    to
});

function title(activity: Activity) {
    switch (activity.type) {
        case 'hotcopper':
        case 'asx-announcement':
            return activity.title ?? '';
        default:
            return '';
    }
}

function body(activity: Activity) {
    switch (activity.type) {
        case 'hotcopper':
            return activity.description ?? '';
        case 'tweet':
            return activity.body ?? '';
        default:
            return '';
    }
}

function author(activity: Activity) {
    switch (activity.type) {
        case 'asx-announcement':
            return 'ASX';
        case 'tweet':
        case 'hotcopper':
            return activity.author?.name ?? '';
        case 'media':
            return activity.source.name ?? '';
        default:
            return '';
    }
}

const config = mkConfig({
    showColumnHeaders: true,
    columnHeaders: [
        { key: 'type', displayLabel: 'Type' },
        { key: 'posted', displayLabel: 'Posted' },
        { key: 'author', displayLabel: 'Author' },
        { key: 'title', displayLabel: 'Title' },
        { key: 'body', displayLabel: 'Body' },
        { key: 'hotcopperSentiment', displayLabel: 'HotCopper Sentiment' },
        { key: 'hotcopperDisclosure', displayLabel: 'HotCopper Disclosure' },
        { key: 'sentiment', displayLabel: 'Sentiment' }
    ]
});

const csv = generateCsv(config)(
    activities.map((activity) => ({
        type: activity.type,
        posted: new Date(activity.posted!).toISOString(),
        title: title(activity),
        body: body(activity),
        author: author(activity),
        sentiment: activity.sentiment ? sentimentScore(activity.sentiment) : '',
        hotcopperSentiment:
            activity.type === 'hotcopper'
                ? (activity.hotcopper?.sentiment ?? 'NONE')
                : '',
        hotcopperDisclosure:
            activity.type === 'hotcopper'
                ? (activity.hotcopper?.disclosure ?? 'UNDISCLOSED')
                : ''
    }))
);

try {
    // fs.writeFileSync(`./${SYMBOL}.csv`, csv as unknown as string);
} catch (err) {
    console.error(err);
}
