import { Pinecone } from '@pinecone-database/pinecone';
import { Activity2 } from '@quarterback/types';
import { chunked, isDefined } from '@quarterback/util';
import { log } from '@quarterback/util/gcp';
import OpenAI from 'openai';
import { isAsx } from '../activities/asx.js';
import { isMedia } from '../activities/news.js';

function chunkTokens(
    tokens: Uint32Array,
    maxTokens: number,
    overlap: number
): Uint32Array[] {
    return Array.from(
        { length: Math.ceil(tokens.length / (maxTokens - overlap)) },
        (_, i) =>
            tokens.slice(i * (maxTokens - overlap), i * (maxTokens - overlap) + maxTokens)
    );
}

export function input(activity: Activity2): string | undefined {
    if (!activity.body) return undefined;

    if (isMedia(activity)) {
        return `${activity.media.source?.name} - ${activity.title}`;
    }

    let input = `${activity.body!}`;

    if ('title' in activity) {
        input = `${activity.title}\n` + input;
    }

    if ('author' in activity) {
        input = `@${activity.author?.userId}\n` + input;
    }

    return input;
}

export function author(activity: Activity2): string {
    if (isMedia(activity)) {
        return activity.media.source!.name!;
    } else if (isAsx(activity)) {
        return 'ASX';
    } else {
        return activity.author?.userId ?? '';
    }
}

export async function create(
    pinecone: Pinecone,
    openAi: OpenAI,
    activities: Array<Activity2>
) {
    const index = pinecone.index('activities');

    for (const chunk of chunked(activities, 64)) {
        log('INFO', 'Computing embeddings for activities', { activities });

        const embeddings = await Promise.all(
            chunk.map(async (activity) => {
                const body = input(activity);

                if (body) {
                    return openAi.embeddings.create({
                        model: 'text-embedding-3-small',
                        input: body
                    });
                } else {
                    return undefined;
                }
            })
        );

        log(
            'INFO',
            `Upserting embeddings for ${embeddings.filter((it) => it !== undefined).length} activities`
        );

        await index.upsert(
            chunk
                .map((activity, index) => {
                    const embedding = embeddings[index]?.data[0]?.embedding;

                    if (embedding) {
                        return {
                            id: activity.id!,
                            metadata: {
                                symbol: activity.symbol!,
                                exchange: activity.exchange!,
                                author: author(activity),
                                posted: new Date(activity.posted!).valueOf()
                            },
                            values: embedding
                        };
                    } else {
                        return undefined;
                    }
                })
                .filter(isDefined)
        );
    }
}
