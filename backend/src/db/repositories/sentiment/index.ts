import * as language from '@google-cloud/language';
import { Activity2, ListedEntity, Sentiment } from '@quarterback/types';
import { groupBy, isDefined } from '@quarterback/util';
import { PgDatabase } from 'drizzle-orm/pg-core';
import OpenAI from 'openai';
import { sentiment } from '../../schema/activities.js';
import { listedEntity } from '../../schema/market.js';
import { isAsx } from '../activities/asx.js';
import { isHotcopper } from '../activities/hotcopper.js';
import { isLinkedInPost } from '../activities/linkedIn.js';
import { isMedia } from '../activities/news.js';
import { isTweet } from '../activities/tweet.js';

async function fetchSentiment(
    client: language.v1.LanguageServiceClient,
    content: string
) {
    const [{ documentSentiment }] = await client.analyzeSentiment({
        document: {
            type: 'PLAIN_TEXT',
            content,
            language: 'en'
        }
    });

    const { score, magnitude } = documentSentiment ?? {};

    if (
        score !== undefined &&
        score !== null &&
        magnitude !== undefined &&
        magnitude !== null
    ) {
        return {
            score: score,
            magnitude: magnitude
        };
    } else {
        throw new Error('Score and magnitude missing');
    }
}

async function createSentiment(content: string) {
    const openAi = new OpenAI();

    const sentiment = await openAi.chat.completions.create({
        messages: [{ role: 'user', content: content }],
        model: 'gpt-4o'
    });

    const { score, magnitude, reason }: Sentiment = Sentiment.parse(
        JSON.parse(sentiment.choices[0].message.content!)
    );

    return {
        score,
        magnitude,
        reason
    };
}

function activityFilter(
    entities: Record<string, Array<ListedEntity>>,
    activity: Activity2
) {
    const entity = entities[activity.symbol!][0];

    if (isTweet(activity)) {
        return (
            entity?.twitterUsername?.toLowerCase() !==
            activity?.author?.userId?.toLocaleLowerCase()
        );
    }

    if (isHotcopper(activity)) {
        return (
            'ASX News'.toLocaleLowerCase() !==
            activity?.author?.userId?.toLocaleLowerCase()
        );
    }

    if (isLinkedInPost(activity)) {
        return (
            entity?.linkedInUsername?.toLowerCase() !==
            activity?.author?.userId?.toLocaleLowerCase()
        );
    }

    return !(isAsx(activity) || isMedia(activity));
}

function getContent(activity: Activity2) {
    let content = `content:${activity.body!}\n`;

    if ('author' in activity) {
        content += `author:${activity.author?.userId}\n`;
    }

    if ('title' in activity) {
        content += `title:${activity.title}\n`;
    }

    if ('hotcopper' in activity && activity.hotcopper) {
        content += `hotcopperSentiment:${activity.hotcopper.sentiment}\n`;
        content += `hotcopperDisclosure:${activity.hotcopper.disclosure}\n`;
    }

    return content;
}

export async function create(
    languageService: language.v1.LanguageServiceClient,
    db: PgDatabase<any>,
    activities: Array<Activity2>
) {
    const entities = groupBy(await db.select().from(listedEntity), (it) => it.symbol);

    if (activities.length > 0) {
        const sentiments = await Promise.allSettled(
            activities
                .filter((activity) => activityFilter(entities, activity))
                .map(async (filteredActivity) => {
                    const sentiment = await fetchSentiment(
                        languageService,
                        filteredActivity.body!
                    );

                    return {
                        activity: filteredActivity.id!,
                        score: sentiment.score,
                        magnitude: sentiment.magnitude
                    };
                })
        );

        const records = sentiments
            .map((item) => {
                if (item && item.status === 'fulfilled' && item.value)
                    return {
                        ...item.value
                    };
            })
            .filter(isDefined);

        if (records.length > 0) {
            const createdSentiments = await db
                .insert(sentiment)
                .values(records)
                .onConflictDoNothing()
                .returning();

            return createdSentiments;
        }
    }

    return [];
}

export async function createWithOpenAI(
    db: PgDatabase<any>,
    activities: Array<Activity2>,
    promptFactory: (data: string) => string
) {
    const entities = groupBy(await db.select().from(listedEntity), (it) => it.symbol);

    if (activities.length > 0) {
        const sentiments = await Promise.allSettled(
            activities
                .filter((activity) => activityFilter(entities, activity))
                .map(async (filteredActivity) => {
                    const sentiment = await createSentiment(
                        promptFactory(getContent(filteredActivity))
                    );

                    return {
                        activity: filteredActivity.id!,
                        score: sentiment.score,
                        magnitude: sentiment.magnitude,
                        reason: sentiment.reason
                    };
                })
        );

        const records = sentiments
            .map((item) => {
                if (item && item.status === 'fulfilled' && item.value)
                    return {
                        ...item.value
                    };
            })
            .filter(isDefined);

        if (records.length > 0) {
            const createdSentiments = await db
                .insert(sentiment)
                .values(records)
                .onConflictDoNothing()
                .returning();

            return createdSentiments;
        }
    }

    return [];
}
