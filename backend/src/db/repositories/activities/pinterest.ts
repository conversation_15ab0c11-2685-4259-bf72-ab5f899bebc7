import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, pinterestPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectPinterestSchema = createSelectSchema(pinterestPost);
export type SelectPinterestSchema = z.infer<typeof SelectPinterestSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isPinterestPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'pinterest'>> {
    return 'pinterest' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'pinterest'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(pinterestPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    title: activity.title!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    PinterestPost: SelectPinterestSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.PinterestPost.activity,
        body: record.PinterestPost.body,
        title: record.PinterestPost.title,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        pinterest: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const pinterestPosts = await db
        .select()
        .from(pinterestPost)
        .innerJoin(author, eq(pinterestPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(pinterestPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    pinterestPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
