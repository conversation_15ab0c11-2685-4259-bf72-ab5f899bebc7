import { Activity2 } from '@quarterback/types';
import { eq, exists } from 'drizzle-orm';
import { PgDatabase } from 'drizzle-orm/pg-core';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { author, callPost } from '../../schema/activities.js';
import { query } from './query.js';

export const selectCallSchema = createSelectSchema(callPost);
export type selectCallSchema = z.infer<typeof selectCallSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isCallPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'call'>> {
    return 'call' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'call'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(callPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    CallPost: selectCallSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.CallPost.activity,
        body: record.CallPost.body,
        likes: record.CallPost.likes,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        call: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const callPosts = await db
        .select()
        .from(callPost)
        .innerJoin(author, eq(callPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(callPost.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    callPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
