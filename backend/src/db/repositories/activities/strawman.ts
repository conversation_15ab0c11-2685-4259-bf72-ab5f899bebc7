import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, strawmanPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectStrawmanSchema = createSelectSchema(strawmanPost);
export type SelectStrawmanSchema = z.infer<typeof SelectStrawmanSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isStrawmanPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'strawman'>> {
    return 'strawman' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'strawman'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(strawmanPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    StrawmanPost: SelectStrawmanSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.StrawmanPost.activity,
        title: record.StrawmanPost.title,
        body: record.StrawmanPost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        strawman: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const strawmanPosts = await db
        .select()
        .from(strawmanPost)
        .innerJoin(author, eq(strawmanPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(strawmanPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    strawmanPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
