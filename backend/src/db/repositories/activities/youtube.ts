import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, youtubePost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectYoutubeSchema = createSelectSchema(youtubePost);
export type SelectYoutubeSchema = z.infer<typeof SelectYoutubeSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isYoutubePost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'youtube'>> {
    return 'youtube' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'youtube'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(youtubePost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    title: activity.title!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    YouTubePost: SelectYoutubeSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.YouTubePost.activity,
        title: record.YouTubePost.title,
        body: record.YouTubePost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        youtube: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const youtubePosts = await db
        .select()
        .from(youtubePost)
        .innerJoin(author, eq(youtubePost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(youtubePost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    youtubePosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
