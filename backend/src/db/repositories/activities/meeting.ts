import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, meetingPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists } from 'drizzle-orm';

export const SelectMeetingSchema = createSelectSchema(meetingPost);
export type SelectMeetingSchema = z.infer<typeof SelectMeetingSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isMeetingPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'meeting'>> {
    return 'meeting' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'meeting'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(meetingPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title,
                    body: activity.body!,
                    likes: activity.likes,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    MeetingPost: SelectMeetingSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.MeetingPost.activity,
        body: record.MeetingPost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        meeting: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const meetingPosts = await db
        .select()
        .from(meetingPost)
        .innerJoin(author, eq(meetingPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(meetingPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    meetingPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
