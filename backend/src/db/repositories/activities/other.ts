import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, otherPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists } from 'drizzle-orm';

export const SelectOtherSchema = createSelectSchema(otherPost);
export type SelectOtherSchema = z.infer<typeof SelectOtherSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isOtherPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'other'>> {
    return 'other' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'other'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(otherPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    OtherPost: SelectOtherSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.OtherPost.activity,
        body: record.OtherPost.body,
        title: record.OtherPost.title ?? undefined,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        other: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const otherPosts = await db
        .select()
        .from(otherPost)
        .innerJoin(author, eq(otherPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(otherPost.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    otherPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
