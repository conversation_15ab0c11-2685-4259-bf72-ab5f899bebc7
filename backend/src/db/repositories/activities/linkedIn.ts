import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, linkedInPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectLinkedInSchema = createSelectSchema(linkedInPost);
export type SelectLinkedInSchema = z.infer<typeof SelectLinkedInSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isLinkedInPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'linkedIn'>> {
    return 'linkedIn' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'linkedIn'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(linkedInPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    title: activity.title,
                    user: activity.author!.key!,
                    userId: activity.author!.userId,
                    image: activity.image,
                    likes: activity.likes ?? 0,
                    comments: activity.comments ?? 0,
                    thread: activity.thread,
                    commentUrn: activity.commentUrn,
                    body: activity.body!
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    LinkedInPost: SelectLinkedInSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.LinkedInPost.activity,
        body: record.LinkedInPost.body,
        title: record.LinkedInPost.title ?? undefined,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        linkedIn: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const linkedInPosts = await db
        .select()
        .from(linkedInPost)
        .innerJoin(author, eq(linkedInPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(linkedInPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    linkedInPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
