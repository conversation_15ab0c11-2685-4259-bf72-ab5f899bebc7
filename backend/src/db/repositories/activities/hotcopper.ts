import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, hotCopperPost, hotCopperThread } from '../../schema/activities.js';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';

export const SelectHotCopperPostSchema = createSelectSchema(hotCopperPost);
export type SelectHotCopperPostSchema = z.infer<typeof SelectHotCopperPostSchema>;

export const SelectHotCopperThreadSchema = createSelectSchema(hotCopperThread);
export type SelectHotCopperThreadSchema = z.infer<typeof SelectHotCopperThreadSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isHotcopper(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'hotcopper'>> {
    return 'hotcopper' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'hotcopper'>>>
) {
    if (activities.length) {
        await db
            .insert(hotCopperThread)
            .values(
                activities.map((activity) => ({
                    thread: activity.hotcopper.thread!.id!,
                    title: activity.title!,
                    views: activity.hotcopper.thread!.views!
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(hotCopperPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    post: activity.hotcopper.id!,
                    thread: activity.hotcopper.thread!.id!,
                    user: activity.author!.key!,
                    likes: activity.likes ?? 0,
                    body: activity.body ?? '',
                    sentiment: activity.hotcopper.sentiment,
                    disclosure: activity.hotcopper.disclosure
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    HotCopperThread: SelectHotCopperThreadSchema;
    HotCopperPost: SelectHotCopperPostSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.HotCopperPost.activity,
        title: record.HotCopperThread.title,
        body: record.HotCopperPost.body,
        author: {
            userId: record.Author.userId,
            name: record.Author.name!,
            key: record.Author.key,
            image: record.Author.image,
            url: record.Author.url,
            followers: record.Author.followers,
            following: record.Author.following
        },
        hotcopper: {
            id: record.HotCopperPost.post,
            disclosure: record.HotCopperPost.disclosure ?? undefined,
            sentiment: record.HotCopperPost.sentiment ?? undefined,
            thread: {
                id: record.HotCopperThread.thread,
                views: record.HotCopperThread.views
            }
        }
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const posts = await db
        .select()
        .from(hotCopperPost)
        .innerJoin(hotCopperThread, eq(hotCopperPost.thread, hotCopperThread.thread))
        .innerJoin(author, eq(hotCopperPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(hotCopperPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    posts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
