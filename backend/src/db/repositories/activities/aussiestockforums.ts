import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, aussiestockforumsPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectAussiestockforumsSchema = createSelectSchema(aussiestockforumsPost);
export type SelectAussiestockforumsSchema = z.infer<typeof SelectAussiestockforumsSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isAussiestockforumsPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'aussiestockforums'>> {
    return 'aussiestockforums' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'aussiestockforums'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(aussiestockforumsPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    AussieStockForums: SelectAussiestockforumsSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.AussieStockForums.activity,
        title: record.AussieStockForums.title,
        body: record.AussieStockForums.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        aussiestockforums: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const aussiestockforumsPosts = await db
        .select()
        .from(aussiestockforumsPost)
        .innerJoin(author, eq(aussiestockforumsPost.user, author.key))
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(eq(aussiestockforumsPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    aussiestockforumsPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
