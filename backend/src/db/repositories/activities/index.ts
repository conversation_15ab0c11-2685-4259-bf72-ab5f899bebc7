import { PgDatabase } from 'drizzle-orm/pg-core';
import { createSelectSchema } from 'drizzle-zod';
import * as files from '../files.js';
import * as z from 'zod';

import { Activity2 } from '@quarterback/types';

import { activity } from '../../schema/activities.js';
import id from '../../schema/id.js';

import UpsertResult from '../UpsertResult.js';

import keyFor from './keyFor.js';
import { query } from './query.js';

import * as archives from './archives.js';
import * as asx from './asx.js';
import * as aussiestockforums from './aussiestockforums.js';
import * as bogleheads from './bogleheads.js';
import * as castbox from './castbox.js';
import * as clubhouse from './clubhouse.js';
import * as discord from './discord.js';
import * as facebook from './facebook.js';
import * as flags from './flags.js';
import * as hotcopper from './hotcopper.js';
import * as iheartradio from './iheartradio.js';
import * as instagram from './instagram.js';
import * as investorhub from './investorhub.js';
import * as linkedIn from './linkedIn.js';
import * as medium from './medium.js';
import * as news from './news.js';
import * as pinterest from './pinterest.js';
import * as quora from './quora.js';
import * as reads from './reads.js';
import * as reddit from './reddit.js';
import * as slack from './slack.js';
import * as snapchat from './snapchat.js';
import * as spotify from './spotify.js';
import * as stocktwits from './stocktwits.js';
import * as strawman from './strawman.js';
import * as telegram from './telegram.js';
import * as tiktok from './tiktok.js';
import * as tradingqna from './tradingqna.js';
import * as tumblr from './tumblr.js';
import * as tweets from './tweet.js';
import * as vimeo from './vimeo.js';
import * as wechat from './wechat.js';
import * as whatsapp from './whatsapp.js';
import * as whirlpoolfinance from './whirlpoolfinance.js';
import * as youtube from './youtube.js';
import * as advfn from './advfn.js';
import * as applePodcast from './applePodcast.js';
import * as audible from './audible.js';
import * as call from './call.js';
import * as meeting from './meeting.js';
import * as presentation from './presentation.js';
import * as event from './event.js';
import * as other from './other.js';

import * as summaries from '../summaries/index.js';

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2>
): Promise<UpsertResult<Activity2>> {
    if (activities.length) {
        const identified: Array<Activity2> = activities.map((it) => ({
            ...it,
            id: it.id ?? id()
        }));

        const inserted = new Set(
            await db
                .insert(activity)
                .values(
                    identified.map((activity) => ({
                        id: activity.id!,
                        key: keyFor(activity),
                        posted: activity.posted!,
                        url: activity.url!,
                        symbol: activity.symbol!,
                        exchange: activity.exchange!,
                        format: activity.format
                    }))
                )
                .onConflictDoNothing()
                .returning()
                .then((it) => it.map((it) => it.id))
        );

        const created = identified.filter((it) => inserted.has(it.id!));

        await Promise.all([
            files.upsert(db, created.filter(files.hasFiles)),
            hotcopper.upsert(db, created.filter(hotcopper.isHotcopper)),
            asx.upsert(db, created.filter(asx.isAsx)),
            news.upsert(db, created.filter(news.isMedia)),
            tweets.upsert(db, created.filter(tweets.isTweet)),
            reddit.upsert(db, created.filter(reddit.isRedditPost)),
            reddit.upsert(db, created.filter(reddit.isRedditComment)),
            linkedIn.upsert(db, created.filter(linkedIn.isLinkedInPost)),
            instagram.upsert(db, created.filter(instagram.isInstagramPost)),
            snapchat.upsert(db, created.filter(snapchat.isSnapchatPost)),
            spotify.upsert(db, created.filter(spotify.isSpotifyPost)),
            stocktwits.upsert(db, created.filter(stocktwits.isStocktwitsPost)),
            strawman.upsert(db, created.filter(strawman.isStrawmanPost)),
            telegram.upsert(db, created.filter(telegram.isTelegramPost)),
            tiktok.upsert(db, created.filter(tiktok.isTiktokPost)),
            tradingqna.upsert(db, created.filter(tradingqna.isTradingqnaPost)),
            tumblr.upsert(db, created.filter(tumblr.isTumblrPost)),
            vimeo.upsert(db, created.filter(vimeo.isVimeoPost)),
            wechat.upsert(db, created.filter(wechat.isWechatPost)),
            whatsapp.upsert(db, created.filter(whatsapp.isWhatsappPost)),
            whirlpoolfinance.upsert(
                db,
                created.filter(whirlpoolfinance.isWhirlpoolfinancePost)
            ),
            youtube.upsert(db, created.filter(youtube.isYoutubePost)),
            advfn.upsert(db, created.filter(advfn.isADVFNPost)),
            applePodcast.upsert(db, created.filter(applePodcast.isApplePodcastPost)),
            audible.upsert(db, created.filter(audible.isAudiblePost)),
            aussiestockforums.upsert(
                db,
                created.filter(aussiestockforums.isAussiestockforumsPost)
            ),
            bogleheads.upsert(db, created.filter(bogleheads.isBogleheadsPost)),
            castbox.upsert(db, created.filter(castbox.isCastboxPost)),
            clubhouse.upsert(db, created.filter(clubhouse.isClubhousePost)),
            discord.upsert(db, created.filter(discord.isDiscordPost)),
            facebook.upsert(db, created.filter(facebook.isFacebookPost)),
            iheartradio.upsert(db, created.filter(iheartradio.isIheartradioPost)),
            investorhub.upsert(db, created.filter(investorhub.isInvestorhubPost)),
            medium.upsert(db, created.filter(medium.isMediumPost)),
            pinterest.upsert(db, created.filter(pinterest.isPinterestPost)),
            quora.upsert(db, created.filter(quora.isQuoraPost)),
            slack.upsert(db, created.filter(slack.isSlackPost)),
            call.upsert(db, created.filter(call.isCallPost)),
            meeting.upsert(db, created.filter(meeting.isMeetingPost)),
            presentation.upsert(db, created.filter(presentation.isPresentationPost)),
            event.upsert(db, created.filter(event.isEventPost)),
            other.upsert(db, created.filter(other.isOtherPost))
        ]);

        const createdActivities = await fetch(db, { ids: created.map((it) => it.id!) });

        return {
            created: createdActivities,
            updated: []
        };
    } else {
        return { created: [], updated: [] };
    }
}

const HotCopperFilter = z.object({
    ids: z.array(z.string())
});

export const FlagFilter = z.object({
    organisation: z.string().optional()
});
export type FlagFilter = z.infer<typeof FlagFilter>;

export const ActivityFetchFilter = z.object({
    expand: z.array(z.enum(['summary', 'flags', 'reads', 'archives'])).optional(),

    symbol: z.string().optional(),
    exchange: z.string().optional(),

    from: z.date().optional(),
    to: z.date().optional(),

    limit: z.number().optional(),
    source: z.enum(['asx']).optional(),

    flags: FlagFilter.optional(),

    hotcopper: HotCopperFilter.optional(),
    ids: z.string().array().optional()
});
export type ActivityFetchFilter = z.infer<typeof ActivityFetchFilter>;

export const SelectActivitySchema = createSelectSchema(activity);
export type SelectActivitySchema = z.infer<typeof SelectActivitySchema>;

function asModel(record: SelectActivitySchema): Activity2 {
    return {
        id: record.id,
        posted: record.posted,
        url: record.url ?? undefined,
        exchange: record.exchange,
        symbol: record.symbol
    };
}

export async function fetch(db: PgDatabase<any>, filter?: ActivityFetchFilter) {
    const cte = query(db, filter);

    const activities = new Map(
        await db
            .with(cte)
            .select()
            .from(cte)
            .then((it) => it.map(asModel))
            .then((it) => it.map((activity) => [activity.id!, activity]))
    );

    await Promise.all([
        asx.expand(db, cte, activities),
        hotcopper.expand(db, cte, activities),
        news.expand(db, cte, activities),
        tweets.expand(db, cte, activities),
        linkedIn.expand(db, cte, activities),
        reddit.expand(db, cte, activities),
        aussiestockforums.expand(db, cte, activities),
        bogleheads.expand(db, cte, activities),
        castbox.expand(db, cte, activities),
        clubhouse.expand(db, cte, activities),
        discord.expand(db, cte, activities),
        facebook.expand(db, cte, activities),
        iheartradio.expand(db, cte, activities),
        instagram.expand(db, cte, activities),
        investorhub.expand(db, cte, activities),
        medium.expand(db, cte, activities),
        pinterest.expand(db, cte, activities),
        quora.expand(db, cte, activities),
        slack.expand(db, cte, activities),
        snapchat.expand(db, cte, activities),
        spotify.expand(db, cte, activities),
        stocktwits.expand(db, cte, activities),
        strawman.expand(db, cte, activities),
        telegram.expand(db, cte, activities),
        tiktok.expand(db, cte, activities),
        tradingqna.expand(db, cte, activities),
        tumblr.expand(db, cte, activities),
        vimeo.expand(db, cte, activities),
        wechat.expand(db, cte, activities),
        whatsapp.expand(db, cte, activities),
        whirlpoolfinance.expand(db, cte, activities),
        youtube.expand(db, cte, activities),
        advfn.expand(db, cte, activities),
        audible.expand(db, cte, activities),
        applePodcast.expand(db, cte, activities),
        call.expand(db, cte, activities),
        meeting.expand(db, cte, activities),
        presentation.expand(db, cte, activities),
        event.expand(db, cte, activities),
        other.expand(db, cte, activities)
    ]);

    if (filter?.expand?.includes('flags'))
        await flags.expand(db, cte, filter?.flags, activities);
    if (filter?.expand?.includes('reads')) await reads.expand(db, cte, activities);
    if (filter?.expand?.includes('archives')) await archives.expand(db, cte, activities);

    if (filter?.expand?.includes('summary')) await summaries.expand(db, cte, activities);

    return [...activities.values()];
}
