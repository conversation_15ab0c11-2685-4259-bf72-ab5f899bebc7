import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, clubhousePost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectClubhouseSchema = createSelectSchema(clubhousePost);
export type SelectClubhouseSchema = z.infer<typeof SelectClubhouseSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isClubhousePost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'clubhouse'>> {
    return 'clubhouse' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'clubhouse'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(clubhousePost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    title: activity.title
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    ClubhousePost: SelectClubhouseSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.ClubhousePost.activity,
        body: record.ClubhousePost.body,
        title: record.ClubhousePost.title ?? undefined,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        clubhouse: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const clubhousePosts = await db
        .select()
        .from(clubhousePost)
        .innerJoin(author, eq(clubhousePost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(clubhousePost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    clubhousePosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
