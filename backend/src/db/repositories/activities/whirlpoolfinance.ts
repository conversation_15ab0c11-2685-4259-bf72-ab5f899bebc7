import { Activity2 } from '@quarterback/types';
import { eq, exists } from 'drizzle-orm';
import { PgDatabase } from 'drizzle-orm/pg-core';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { author, whirlpoolFinancePost } from '../../schema/activities.js';
import { query } from './query.js';

export const SelectWhirlpoolfinanceSchema = createSelectSchema(whirlpoolFinancePost);
export type SelectWhirlpoolfinanceSchema = z.infer<typeof SelectWhirlpoolfinanceSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isWhirlpoolfinancePost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'whirlpoolfinance'>> {
    return 'whirlpoolfinance' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'whirlpoolfinance'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(whirlpoolFinancePost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    title: activity.title!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    WhirlpoolFinancePost: SelectWhirlpoolfinanceSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.WhirlpoolFinancePost.activity,
        body: record.WhirlpoolFinancePost.body,
        title: record.WhirlpoolFinancePost.title,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        whirlpoolfinance: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const whirlpoolfinancePosts = await db
        .select()
        .from(whirlpoolFinancePost)
        .innerJoin(author, eq(whirlpoolFinancePost.user, author.key))
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(eq(whirlpoolFinancePost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    whirlpoolfinancePosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
