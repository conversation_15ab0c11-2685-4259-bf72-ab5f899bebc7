import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, vimeoPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectVimeoSchema = createSelectSchema(vimeoPost);
export type SelectVimeoSchema = z.infer<typeof SelectVimeoSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isVimeoPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'vimeo'>> {
    return 'vimeo' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'vimeo'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(vimeoPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    title: activity.title!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    VimeoPost: SelectVimeoSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.VimeoPost.activity,
        body: record.VimeoPost.body,
        title: record.VimeoPost.title,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        vimeo: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const vimeoPosts = await db
        .select()
        .from(vimeoPost)
        .innerJoin(author, eq(vimeoPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(vimeoPost.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    vimeoPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
