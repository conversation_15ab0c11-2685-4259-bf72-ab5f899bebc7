import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { newsArticle, newsSource } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { SelectASXAnnouncementSchema } from './asx.js';
import { query } from './query.js';
import { eq, exists } from 'drizzle-orm';

export const SelectNewsSchema = createSelectSchema(newsArticle);
export type SelectNewsSchema = z.infer<typeof SelectNewsSchema>;

export const SelectNewsSourceSchema = createSelectSchema(newsSource);
export type SelectNewsSourceSchema = z.infer<typeof SelectNewsSourceSchema>;

export function isMedia(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'media'>> {
    return 'media' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'media'>>>
) {
    if (activities.length) {
        await db
            .insert(newsSource)
            .values(
                activities.map((activity) => ({
                    name: activity.media.source!.name!,
                    url: activity.media.source!.domain!
                }))
            )
            .onConflictDoNothing();

        await db.insert(newsArticle).values(
            activities.map((activity) => ({
                activity: activity.id!,
                title: activity.title ?? '',
                source: activity.media.source!.domain,
                summary: activity.body ?? '',
                image: activity.media.image
            }))
        );
    }
}

function asModel(record: {
    NewsArticle: SelectNewsSchema;
    NewsSource: SelectNewsSourceSchema;
}): Activity2 {
    return {
        id: record.NewsArticle.activity,
        title: record.NewsArticle.title,
        body: record.NewsArticle.summary ?? undefined,
        media: {
            image: record.NewsArticle.image ?? undefined,
            source: {
                name: record.NewsSource.name,
                domain: record.NewsSource.url
            }
        }
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const articles = await db
        .select()
        .from(newsArticle)
        .innerJoin(newsSource, eq(newsArticle.source, newsSource.url))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(newsArticle.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    articles.forEach((article) => {
        const activity = activities.get(article.id!);

        activities.set(article.id!, {
            ...activity,
            ...article
        });
    });
}
