import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { asxAnnouncement } from '../../schema/activities.js';
import { query } from './query.js';
import { and, eq, exists } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { SelectActivitySchema } from './index.js';

export const SelectASXAnnouncementSchema = createSelectSchema(asxAnnouncement);
export type SelectASXAnnouncementSchema = z.infer<typeof SelectASXAnnouncementSchema>;

export function isAsx(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'asx'>> {
    return 'asx' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'asx'>>>
) {
    if (activities.length) {
        await db
            .insert(asxAnnouncement)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    title: activity.title!,
                    body: activity.body,
                    priceSensitive: activity.asx.sensitive ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: SelectASXAnnouncementSchema): Activity2 {
    return {
        id: record.activity,
        title: record.title,
        asx: {
            sensitive: record.priceSensitive
        }
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const announcements = await db
        .select()
        .from(asxAnnouncement)
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(eq(asxAnnouncement.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    announcements.forEach((announcement) => {
        const activity = activities.get(announcement.id!);

        activities.set(announcement.id!, {
            ...activity,
            ...announcement
        });
    });
}
