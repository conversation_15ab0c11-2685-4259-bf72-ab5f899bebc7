import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, mediumPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectMediumSchema = createSelectSchema(mediumPost);
export type SelectMediumSchema = z.infer<typeof SelectMediumSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isMediumPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'medium'>> {
    return 'medium' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'medium'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(mediumPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    MediumPost: SelectMediumSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.MediumPost.activity,
        title: record.MediumPost.title,
        body: record.MediumPost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        medium: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const mediumPosts = await db
        .select()
        .from(mediumPost)
        .innerJoin(author, eq(mediumPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(mediumPost.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    mediumPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
