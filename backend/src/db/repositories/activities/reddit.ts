import { Activity2 } from '@quarterback/types';
import { eq, exists } from 'drizzle-orm';
import { PgDatabase } from 'drizzle-orm/pg-core';
import { createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import { author, redditComment, redditPost } from '../../schema/activities.js';
import { query } from './query.js';

type RedditPostActivity = Activity2 & Required<Pick<Activity2, 'redditPost'>>;
type RedditCommentActivity = Activity2 & Required<Pick<Activity2, 'redditComment'>>;

export const SelectRedditPostSchema = createSelectSchema(redditPost);
export type SelectRedditPostSchema = z.infer<typeof SelectRedditPostSchema>;

export const SelectRedditCommentSchema = createSelectSchema(redditComment);
export type SelectRedditCommentSchema = z.infer<typeof SelectRedditCommentSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isRedditPost(activity: Activity2): activity is RedditPostActivity {
    return 'redditPost' in activity;
}

export function isRedditComment(activity: Activity2): activity is RedditCommentActivity {
    return 'redditComment' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<RedditCommentActivity>
): Promise<void>;
export async function upsert(
    db: PgDatabase<any>,
    activities: Array<RedditPostActivity>
): Promise<void>;
export async function upsert(
    db: PgDatabase<any>,
    activities: Array<RedditPostActivity> | Array<RedditCommentActivity>
) {
    const posts = activities.filter(isRedditPost);
    const comments = activities.filter(isRedditComment);

    if (posts.length) {
        await db
            .insert(author)
            .values(
                posts.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(redditPost)
            .values(
                posts.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body ?? '',
                    id: activity.redditPost.id!,
                    title: activity.title ?? '',
                    score: activity.redditPost.score ?? 0,
                    ratio: activity.redditPost.ratio ?? 0,
                    subreddit: activity.redditPost.subreddit!,
                    image: activity.redditPost.image
                }))
            )
            .onConflictDoNothing();
    }

    if (comments.length) {
        await db
            .insert(author)
            .values(
                comments.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(redditComment)
            .values(
                comments.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    post: activity.redditComment.post!,
                    body: activity.body ?? '',
                    id: activity.redditComment.id!,
                    parent: activity.redditComment.parent!,
                    score: activity.redditComment.score ?? 0
                }))
            )
            .onConflictDoNothing();
    }
}

function asModelPost(record: {
    RedditPost: SelectRedditPostSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.RedditPost.activity,
        title: record.RedditPost.title,
        body: record.RedditPost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        redditPost: {
            id: record.RedditPost.id,
            score: record.RedditPost.score,
            ratio: record.RedditPost.ratio,
            subreddit: record.RedditPost.subreddit!,
            image: record.RedditPost?.image ?? undefined
        }
    };
}

function asModelComment(record: {
    RedditComment: SelectRedditCommentSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.RedditComment.activity,
        body: record.RedditComment.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        redditComment: {
            id: record.RedditComment.id,
            post: record.RedditComment?.post ?? undefined,
            parent: record.RedditComment.parent,
            score: record.RedditComment.score
        }
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const redditPosts = await db
        .select()
        .from(redditPost)
        .innerJoin(author, eq(redditPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(redditPost.activity, cte.id)))
        )
        .then((it) => it.map(asModelPost));

    const redditComments = await db
        .select()
        .from(redditComment)
        .innerJoin(author, eq(redditComment.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(redditComment.activity, cte.id))
            )
        )
        .then((it) => it.map(asModelComment));

    redditPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });

    redditComments.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
