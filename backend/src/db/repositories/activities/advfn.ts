import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, aussiestockforumsPost, advfnPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectADVFNforumsSchema = createSelectSchema(advfnPost);
export type SelectADVFNforumsSchema = z.infer<typeof SelectADVFNforumsSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isADVFNPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'advfn'>> {
    return 'advfn' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'advfn'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(advfnPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    ADVFNPost: SelectADVFNforumsSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.ADVFNPost.activity,
        title: record.ADVFNPost.title,
        body: record.ADVFNPost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        advfn: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const advfnPosts = await db
        .select()
        .from(advfnPost)
        .innerJoin(author, eq(advfnPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(advfnPost.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    advfnPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
