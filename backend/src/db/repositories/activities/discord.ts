import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, discordPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectDiscordSchema = createSelectSchema(discordPost);
export type SelectDiscordSchema = z.infer<typeof SelectDiscordSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isDiscordPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'discord'>> {
    return 'discord' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'discord'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(discordPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    DiscordPost: SelectDiscordSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.DiscordPost.activity,
        body: record.DiscordPost.body,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        discord: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const discordPosts = await db
        .select()
        .from(discordPost)
        .innerJoin(author, eq(discordPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(discordPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    discordPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
