import { Activity2 } from '@quarterback/types';
import { eq, exists } from 'drizzle-orm';
import { PgDatabase } from 'drizzle-orm/pg-core';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { author, eventPost } from '../../schema/activities.js';
import { query } from './query.js';

export const SelectEventSchema = createSelectSchema(eventPost);
export type SelectEventSchema = z.infer<typeof SelectEventSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isEventPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'event'>> {
    return 'event' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'event'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(eventPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title,
                    body: activity.body!,
                    likes: activity.likes,
                    image: activity.image
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    EventPost: SelectEventSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.EventPost.activity,
        body: record.EventPost.body,
        title: record.EventPost?.title ?? undefined,
        likes: record.EventPost.likes,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        event: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const eventPosts = await db
        .select()
        .from(eventPost)
        .innerJoin(author, eq(eventPost.user, author.key))
        .where(
            exists(db.with(cte).select().from(cte).where(eq(eventPost.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    eventPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
