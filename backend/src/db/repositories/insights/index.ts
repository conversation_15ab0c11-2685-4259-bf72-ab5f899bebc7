import { Activity2, Activity, WordCloudItem } from '@quarterback/types';
import { PgDatabase } from 'drizzle-orm/pg-core';
import OpenAI from 'openai';
import crypto from 'crypto';
import { insights } from '../../schema/ai.js';
import { and, eq, sql } from 'drizzle-orm';
import { encoding_for_model } from 'tiktoken';
import * as prompts from './prompts.js';
import { listedEntity } from '../../schema/market.js';
import { processWordForWordCloud } from './wordCloud.js';

const encoding = encoding_for_model('gpt-4o');

export async function chatter(
    db: PgDatabase<any>,
    openAi: OpenAI,
    organisation: string,
    symbol: string,
    activities: Array<Activity2>,
    limit?: number,
    type: string = 'mixed',
    extended?: boolean
): Promise<string> {
    const content = activities
        .map(
            (activity) =>
                `${activity.title} - ${activity.author?.userId ?? ''}\n${activity.body}\nSymbol: ${activity.symbol}\nExchange: ${activity.exchange}`
        )
        .join('\n\n');

    return await fetchInsights(
        db,
        openAi,
        organisation,
        symbol,
        content,
        extended ? prompts.threePartChatterSummary : prompts.chatterSummary,
        limit,
        type
    );
}

export async function topicsExtraction(
    db: PgDatabase<any>,
    openAi: OpenAI,
    organisation: string,
    symbol: string,
    activities: Array<Activity2>
): Promise<string> {
    const content = activities
        .map(
            (activity) =>
                `${activity.title} - ${activity.author?.userId ?? ''}\n${activity.body}\nSymbol: ${activity.symbol}\nExchange: ${activity.exchange}`
        )
        .join('\n\n');

    return await fetchInsights(
        db,
        openAi,
        organisation,
        symbol,
        content,
        prompts.chatterTopicsExtraction,
        undefined,
        undefined,
        true
    );
}

/**
 * Given a prompt factory and context string, either fetch a cached insight
 * or generate a new one
 */
async function fetchInsights(
    db: PgDatabase<any>,
    openAi: OpenAI,
    organisation: string,
    symbolWithExchange: string,
    context: string,
    promptFactory: (
        context: string,
        about?: string | null | undefined,
        limt?: number,
        type?: string
    ) => string,
    limit?: number,
    type: string = 'mixed',
    jsonOutput: boolean = false
): Promise<string> {
    const truncated = new TextDecoder().decode(
        encoding.decode(encoding.encode(context).slice(-50000))
    );

    const [symbol, exchange] = symbolWithExchange.split(':');

    const about = await db
        .select({
            about: listedEntity.about
        })
        .from(listedEntity)
        .where(and(eq(listedEntity.symbol, symbol), eq(listedEntity.exchange, exchange)))
        .then((it) => it[0]?.about);

    const prompt = promptFactory(truncated, about, limit, type);
    const hash = crypto.createHash('sha256').update(prompt).digest('hex');

    const [existing] = await db
        .select()
        .from(insights)
        .where(and(eq(insights.hash, hash), eq(insights.organisation, organisation)));

    if (existing) {
        if (jsonOutput) {
            return JSON.parse(existing.summary);
        }

        return existing.summary;
    }
    const summary = await openAi.chat.completions.create({
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o-mini'
    });

    await db
        .insert(insights)
        .values([
            {
                organisation,
                hash,
                summary: summary.choices[0].message.content!
            }
        ])
        .onConflictDoUpdate({
            target: [insights.hash, insights.organisation],
            set: { summary: sql`excluded.summary` }
        });

    if (jsonOutput) {
        return JSON.parse(summary.choices[0].message.content!);
    }

    return summary.choices[0].message.content!;
}

export async function wordCloud(
    activities: Array<Activity>
): Promise<Array<WordCloudItem>> {
    return processWordForWordCloud(activities);
}
