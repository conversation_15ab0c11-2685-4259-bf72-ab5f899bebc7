import { Activity, WordCloudItem } from '@quarterback/types';
import natural from 'natural';

import nlp from 'compromise';

export function processWordForWordCloud(
    activities: Array<Activity>
): Array<WordCloudItem> {
    const wordData = new Map<
        string,
        {
            word: string;
            frequency: number;
            sources: Set<string>;
            sentimentScores: number[];
            posts: string[];
        }
    >();

    const activityWordPairs: Array<{
        words: string[];
        type: string;
        sentimentScore: number;
        id: string;
    }> = activities.map((activity) => ({
        words: extractWords(getContent(activity)),
        type: activity.type,
        sentimentScore: activity.sentiment?.score ?? 0,
        id: activity.id!
    }));

    for (const { words, type, sentimentScore, id } of activityWordPairs) {
        for (const word of words) {
            let data = wordData.get(word);
            if (!data) {
                data = {
                    word,
                    frequency: 0,
                    sources: new Set(),
                    sentimentScores: [],
                    posts: []
                };
                wordData.set(word, data);
            }

            data.frequency++;
            data.sources.add(type);
            data.sentimentScores.push(sentimentScore);
            data.posts.push(id);
        }
    }

    // Calculate average sentiment and convert to final format
    return Array.from(wordData.values()).map((data) => ({
        word: data.word,
        frequency: data.frequency,
        sources: Array.from(data.sources),
        averageSentiment:
            data.sentimentScores.reduce((a: number, b: number) => a + b, 0) /
            data.sentimentScores.length,
        activitiesCount: data.posts.length
    }));
}

function extractWords(content: string): Array<string> {
    const doc = nlp(content);
    const meaningfulWords = [
        ...doc.nouns().out('array'),
        ...doc.adjectives().out('array'),
        ...doc.verbs().out('array')
    ];

    return meaningfulWords
        .map((word) => word.toLowerCase())
        .filter((word) => word.length > 2)
        .filter((word) => !natural.stopwords.includes(word))
        .filter((word) => natural.PorterStemmer.stem(word));
}

function getContent(activity: Activity): string {
    if ('body' in activity && activity?.body?.length) {
        return activity.body;
    }

    if ('title' in activity && activity?.title?.length) {
        return activity.title;
    }

    return '';
}
