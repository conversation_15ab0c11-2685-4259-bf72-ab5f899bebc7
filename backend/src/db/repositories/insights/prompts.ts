export function broadcastSummary(context: string) {
    return `You are an expert in summarising social media chatter about companies listed on stock exchanges.
We are using this investor chatter to help the company improve their investor relations strategy by understanding what investors care about.

Below you find a set of social media posts about a listed company.
---
${context}
---
Total output will be a brief and concise 2 paragraph summary of the above chatter, identifying main topics of chatter, concerns, and negative sentiment differentiated between criticism directed at the company and frustrations with external factors. 
Start with an overall summary and keep the paragraphs brief and to the point. Highlight the key phrases in bold that a user would likely click on to perform a search. Only highlight phrases that are specific, actionable, or central to the topic, ensuring they would yield relevant search results. No waffling.
`;
}

export function chatterSummary(
    context: string,
    about?: string | null | undefined,
    limit?: number,
    type: string = 'mixed'
) {
    return `You are an expert in summarising social media chatter about companies listed on stock exchanges.
We are using this investor chatter to help the company improve their investor relations strategy by understanding what investors care about.

Below you find a set of social media posts about a listed company.
---
${context}
---

${
    about?.length
        ? `In addition to the investor chatter, you know the following information about the company taken from their website:
---
${about}
---`
        : ''
}

Total output will be a brief and concise ${limit && limit > 0 ? limit + ' words (broken down into paragraphs where required)' : '2 paragraph'} summary of the above chatter, identifying main topics of chatter, concerns, and negative sentiment differentiated between criticism directed at the company and frustrations with external factors. 
Start with an overall summary and keep the paragraphs brief and to the point. Highlight the key phrases in bold that a user would likely click on to perform a search. Only highlight phrases that are specific, actionable, or central to the topic, ensuring they would yield relevant search results. No waffling. 

The type of the posts you are reading is ${type}. You may only mention the type in the summary if the type is not 'mixed'. If the type is 'mixed', avoid phrases like social media, chatter, broadcasts, etc.
`;
}

export function sentimentAnalysis(data: string) {
    return `You are an expert in sentiment analysis for financial data and social media chatter about publicly listed companies. Analyze the sentiment of the following social media post strictly in the context of financial markets, where negative wording (e.g., price drops, market volatility) may still indicate positive sentiment if it suggests opportunity (e.g., undervaluation, buying signals, confidence in recovery). Focus on intent, financial implications, and inferred investor outlook—not just emotional tone.
-----
${data}
-----
Return the result with **only** this exact JSON format, with double quotes on all property names and values where appropriate.Do NOT include any code block markers
{"score":<decimal between -1.00 and +1.00 with two decimal places>,"magnitude":<positive decimal>,"reason":"<short explanation of why this score and magnitude were assigned, based on financial context and implied sentiment>"}`;
}

export function threePartChatterSummary(
    context: string,
    about?: string | null | undefined
) {
    return `You are a capital markets analyst specializing in summarizing investor sentiment from social media and other online platforms. You are reviewing investor commentary related to companies listed on stock exchanges (e.g. the ASX).
The goal is to extract key insights from investor sentiment to help the company improve investor relations by understanding what themes are resonating, where confidence lies, and where concerns are emerging.
Below is a set of investor commentary and posts related to the company:
${context}
${
    about?.length
        ? `In addition, here is background context from the company’s official website:

${about}
—`
        : ''
}

Write a three-part summary:
**Overall Summary of Chatter for <TICKER>** \n
<A concise paragraph summarizing the main investor focus, tone, and recurring themes in the commentary. Mention both positive momentum and broader concerns.>
	
**Positive Chatter Summary** \n
<Summarize positive sentiment, emphasizing strong operational results, investor optimism, or strategic advantages. Highlight specific drivers of optimism, such as high-grade results, milestones, partnerships, or growth potential.
	
**Negative Chatter Summary** \n
<Summarize critical sentiment, differentiating between concerns directed at the company (e.g. strategy, communication) and those tied to external market factors (e.g. pricing, valuation, geopolitical risks). Keep the tone factual and balanced.

Use bold to highlight key terms, especially those that could yield useful results in a search (e.g. JV developments, iron ore assets, South America pricing). Also use bold for the section names like Positive Chatter Summary,etc and start the content for each section from a new line. Do not highlight generalities or fluff.>
Tone should be professional, neutral, and insightful—no casual or promotional language.`;
}

export function chatterTopicsExtraction(context: string) {
    return `You are an expert in extracting topics from social media chatter about companies listed on stock exchanges. Act as a data scientist performing topic modeling on document collections. Discover and quantify the most prominent topics for visualization.
We are using this investor chatter to help the company improve their investor relations strategy by understanding what investors care about.

Below you find a set of social media posts about a listed company.
---
${context}
---
ANALYSIS APPROACH:
1. Read through all documents comprehensively
2. Identify recurring themes, concepts, and subjects
4. Quantify each topic's presence across the document set
5. Rank by significance and frequency

QUALITY CRITERIA:
- Topics should be mutually exclusive where possible
- Names should be immediately understandable to general audience
- Focus on content themes, not document structure or metadata
- Balance specificity with generalizability

OUTPUT: Strictly return the result with **only** this exact JSON format having an array sorted by count (descending), with double quotes on all property names and values where appropriate.Do NOT include any code block markers and nothing else:
[{"topic": "Topic Name", "count": <count>}]
`;
}
