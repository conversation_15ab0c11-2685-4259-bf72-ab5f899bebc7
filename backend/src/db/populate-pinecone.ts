import { Pinecone } from '@pinecone-database/pinecone';
import db, { queryClient } from './index.js';
import * as activitiesRepository from './repositories/activities.js';
import { chunked, isDefined } from '@quarterback/util';
import OpenAI from 'openai';
import { Activity } from '@quarterback/types';
import { listedEntity } from './schema/market.js';
import { pinecone } from './pinecone.js';
import { and, eq } from 'drizzle-orm';
import arg from 'arg';

const openAi = new OpenAI();

// text-embedding-3-small 1536
await pinecone.createIndex({
    name: 'activities',
    dimension: 1536,
    metric: 'cosine',
    spec: {
        serverless: {
            cloud: 'gcp',
            region: 'us-central1'
        }
    },
    suppressConflicts: true,
    waitUntilReady: true
});

const index = pinecone.index('activities');

function input(activity: Activity): string | undefined {
    switch (activity.type) {
        case 'asx-announcement':
            return activity.title;
        case 'tweet':
            return `@${activity.author?.userId}\n${activity.body}`;
        case 'linkedIn':
            return `@${activity.author?.userId}\n${activity.body}`;
        case 'hotcopper':
            if (activity.description.trim()) {
                return `${activity.title} - ${activity.author?.userId}\n${activity.description}`;
            } else {
                return undefined;
            }
        case 'media':
            return `${activity.source.name} - ${activity.title}`;
    }
}

function author(activity: Activity): string {
    switch (activity.type) {
        case 'asx-announcement':
            return 'ASX';
        case 'media':
            return activity.source.name;
        default:
            return activity.author?.userId ?? '';
    }
}

const args = arg({
    '--symbol': String
});

const symbol = args['--symbol'];

if (!symbol) {
    throw new Error('Symbol is a required argument');
}

const entities = await db
    .select()
    .from(listedEntity)
    .where(and(eq(listedEntity.symbol, symbol), eq(listedEntity.exchange, 'ASX')));

for (const entity of entities) {
    console.log(`Populating Pinecone for ${entity.name}...`);

    const activities = await activitiesRepository.read(
        db,
        '',
        '',
        entity.symbol,
        entity.exchange,
        {}
    );

    for (const chunk of chunked(activities, 64)) {
        console.log('Fetching embeddings...');
        const embeddings = await Promise.all(
            chunk.map(async (activity) => {
                const body = input(activity);

                if (body) {
                    return openAi.embeddings.create({
                        model: 'text-embedding-3-small',
                        input: body
                    });
                } else {
                    return undefined;
                }
            })
        );

        console.log('Embeddings fetched');
        console.log('Upserting pinecone indexes...');
        await index.upsert(
            chunk
                .map((activity, index) => {
                    const embedding = embeddings[index]?.data[0]?.embedding;

                    if (embedding) {
                        return {
                            id: activity.id!,
                            metadata: {
                                symbol: entity.symbol,
                                exchange: entity.exchange,
                                author: author(activity),
                                posted: new Date(activity.posted!).valueOf()
                            },
                            values: embedding
                        };
                    } else {
                        return undefined;
                    }
                })
                .filter(isDefined)
        );

        console.log('Indexes upserted');

        await new Promise<void>((resolve) => {
            setTimeout(() => resolve(), 1000);
        });
    }
}

console.log('Done!');
await queryClient.end();
