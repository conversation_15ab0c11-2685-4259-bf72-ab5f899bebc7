{"name": "@quarterback/backend", "module": "index.ts", "type": "module", "scripts": {"local": "pnpm build && dotenvx run --env-file=.env.local -- pnpm start", "drizzle:generate:local": "pnpm build && dotenvx run --env-file=.env.local -- pnpm drizzle-kit generate", "drizzle:migrate:local": "pnpm build && dotenvx run --env-file=.env.local -- node ./dist/db/migrate.js", "drizzle:studio:local": "dotenvx run --env-file=.env.local -- pnpm drizzle-kit studio", "dev": "pnpm build && dotenvx run --env-file=.env.development -- pnpm start", "drizzle:generate:dev": "pnpm build && dotenvx run --env-file=.env.development -- pnpm drizzle-kit generate", "drizzle:generatecustom": "pnpm drizzle-kit generate --custom", "drizzle:migrate:dev": "pnpm build && dotenvx run --env-file=.env.development -- node ./dist/db/migrate.js", "drizzle:studio:dev": "dotenvx run --env-file=.env.development -- pnpm drizzle-kit studio", "drizzle:studio:prod": "dotenvx run --env-file=.env.production -- pnpm drizzle-kit studio", "pinecone:backfill:dev": "pnpm build && dotenvx run --env-file=.env.development -- node ./dist/db/populate-pinecone.js", "drizzle:migrate:prod": "pnpm build && dotenvx run --env-file=.env.production -- node ./dist/db/migrate.js", "pinecone:backfill:prod": "pnpm build && dotenvx run --env-file=.env.production -- node ./dist/db/populate-pinecone.js", "export:prod": "pnpm build && dotenvx run --env-file=.env.production.local -- node ./dist/db/export.js", "import:prod": "pnpm build && dotenvx run --env-file=.env.production.local -- node ./dist/db/import.js", "start": "node ./dist/index.js", "build": "pnpm tsc"}, "files": ["dist"], "peerDependencies": {"typescript": "^5.0.0"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@date-fns/tz": "^1.2.0", "@dotenvx/dotenvx": "^0.44.1", "@fast-csv/parse": "^5.0.0", "@google-cloud/language": "^6.4.0", "@google-cloud/pubsub": "^4.3.3", "@google-cloud/run": "^1.2.0", "@google-cloud/storage": "^7.16.0", "@google/events": "^5.4.0", "@pinecone-database/pinecone": "^3.0.0", "@quarterback/types": "workspace:*", "@quarterback/util": "workspace:*", "@react-email/components": "^0.0.22", "@sentry/node": "^8.17.0", "@types/jsonwebtoken": "^9.0.6", "arg": "^5.0.2", "base-x": "^4.0.0", "compromise": "^14.14.4", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.1.3", "drizzle-orm": "^0.31.2", "drizzle-zod": "^0.5.1", "export-to-csv": "^1.3.0", "express": "^4.19.2", "google-auth-library": "^9.10.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "natural": "^8.1.0", "openai": "^4.55.3", "parse-domain": "^8.0.2", "pg-connection-string": "^2.6.4", "postgres": "^3.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "resend": "^3.5.0", "tiktoken": "^1.0.15", "tsx": "^4.8.2", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.12.7", "@types/pg": "^8.11.6", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "drizzle-kit": "^0.22.6", "typescript": "^5.4.5"}}