import z from 'zod';
import { Sentiment } from './Sentiment.js';

export const NewsSource = z.object({
    name: z.string(),
    url: z.string(),
    logo: z.string().nullable()
});
export type NewsSource = z.infer<typeof NewsSource>;

export const MediaArticle = z.object({
    id: z.string().optional(),
    posted: z.coerce.date(),
    title: z.string(),
    summary: z.string().nullable(),
    url: z.string().url(),
    image: z.string().url().nullable(),
    source: NewsSource,
    sentiment: Sentiment.optional()
});
export type MediaArticle = z.infer<typeof MediaArticle>;
