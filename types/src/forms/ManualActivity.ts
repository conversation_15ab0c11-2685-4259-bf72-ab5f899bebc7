import { z } from 'zod';
import { ActivityFile, activitySource, author, format } from '@quarterback/types';

const CommonActivityFields = {
    title: z.string().optional(),
    body: z.string().min(1, 'Required'),
    url: z
        .string()
        .transform((val) => (val === '' || val === null ? undefined : val))
        .pipe(z.string().url('Must be a valid URL').optional()),
    image: z.string().url('Must be a valid image URL').optional().or(z.literal('')),
    posted: z
        .string()
        .min(1, 'Required')
        .refine((val) => !val || !isNaN(Date.parse(val)), {
            message: 'Invalid date format'
        }),
    format: format,
    source: activitySource.optional(),
    author: author.optional(),
    likes: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Likes must be a number'
        })
        .optional(),
    views: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Views must be a number'
        })
        .optional(),
    files: ActivityFile.array().optional()
};

// Base schema for social media activities (requires author)
export const BaseActivityFormDataWithAuthor = z.object({
    ...CommonActivityFields,
    author: author.refine((val) => val !== undefined && val !== null, {
        message: 'Required'
    })
});

// Base schema for news (no author required)
export const BaseActivityFormDataWithoutAuthor = z.object({
    ...CommonActivityFields,
    title: z.string().min(1, 'Required')
});

export const CallFormData = BaseActivityFormDataWithAuthor.extend({
    format: z.literal('call')
});

export const MeetingFormData = BaseActivityFormDataWithAuthor.extend({
    format: z.literal('meeting')
});

export const PresentationFormData = BaseActivityFormDataWithAuthor.extend({
    format: z.literal('presentation')
});

export const EventFormData = BaseActivityFormDataWithAuthor.extend({
    format: z.literal('event')
});

export const OtherFormData = BaseActivityFormDataWithAuthor.extend({
    format: z.literal('other')
});

export const TwitterFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('twitter.com') || url.includes('x.com');
            },
            {
                message: 'Must be a Twitter URL'
            }
        )
});

export const HotCopperFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('hotcopper.com.au');
            },
            {
                message: 'Must be a HotCopper URL'
            }
        ),
    disclosure: z.string().min(1, 'Disclosure is required'),
    sentiment: z.string().min(1, 'Sentiment is required'),
    thread: z.string().min(1, 'Required'),
    threadViews: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Thread views must be a number'
        })
        .optional()
});

export const LinkedInFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('linkedin.com');
            },
            {
                message: 'Must be a LinkedIn URL'
            }
        ),
    comments: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Comments must be a number'
        })
        .optional()
});

export const RedditFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('reddit.com');
            },
            {
                message: 'Must be a Reddit URL'
            }
        ),
    subreddit: z.string().min(1, 'Subreddit is required'),
    score: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Score must be a number'
        })
        .optional(),
    ratio: z
        .string()
        .refine(
            (val) =>
                !val || (!isNaN(Number(val)) && Number(val) >= 0 && Number(val) <= 1),
            { message: 'Ratio must be a number between 0 and 1' }
        )
        .optional()
});

export const MediaFormData = BaseActivityFormDataWithoutAuthor.extend({
    newsSource: z.object({
        name: z.string().optional(),
        logo: z.string().nullable().optional(),
        url: z.string().optional()
    }),

    url: z.string().url('Must be a valid URL')
});

export const ASXFormData = BaseActivityFormDataWithoutAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .regex(
            /https:\/\/(?:www\.)?(?:asx\.com\.au\/(?:asxpdf\/\d+\/pdf\/[a-zA-Z0-9]+\.pdf|market-announcements\/company\/[a-zA-Z0-9]+\/[a-zA-Z0-9-]+)|announcements\.asx\.com\.au\/(?:asxpdf\/\d+\/pdf\/[a-zA-Z0-9]+\.pdf|asx\/[a-zA-Z0-9]+(?:\/[a-zA-Z0-9-]+)?)|www2\.asx\.com\.au\/markets\/company\/[a-zA-Z0-9]+\/announcements(?:\/[a-zA-Z0-9-]+)?|www2\.asx\.com\.au\/markets\/trade-our-cash-market\/announcements\.(?:[a-zA-Z0-9]+))/,
            'Invalid ASX announcement URL'
        ),
    sensitive: z.boolean().optional()
});

export const FacebookFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('facebook.com');
            },
            {
                message: 'Must be a Facebook URL'
            }
        )
});

export const InstagramFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('instagram.com');
            },
            {
                message: 'Must be an Instagram URL'
            }
        )
});

export const YouTubeFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('youtube.com') || url.includes('youtu.be');
            },
            {
                message: 'Must be a YouTube URL'
            }
        )
});

export const TikTokFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tiktok.com');
            },
            {
                message: 'Must be a TikTok URL'
            }
        )
});

export const DiscordFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('discord.com') || url.includes('discord.gg');
            },
            {
                message: 'Must be a Discord URL'
            }
        )
});

export const TelegramFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('t.me') || url.includes('telegram.me');
            },
            {
                message: 'Must be a Telegram URL'
            }
        )
});

export const MediumFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('medium.com');
            },
            {
                message: 'Must be a Medium URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const QuoraFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('quora.com');
            },
            {
                message: 'Must be a Quora URL'
            }
        )
});

export const SlackFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('slack.com');
            },
            {
                message: 'Must be a Slack URL'
            }
        )
});

export const ADVFNFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('advfn.com');
            },
            {
                message: 'Must be an ADVFN URL'
            }
        ),

    title: z.string().min(1, 'Required')
});

export const ApplePodcastFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('podcasts.apple.com');
            },
            {
                message: 'Must be an Apple Podcast URL'
            }
        )
});

export const AudibleFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('audible.com');
            },
            {
                message: 'Must be an Audible URL'
            }
        )
});

export const AussieStockForumsFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('aussiestockforums.com');
            },
            {
                message: 'Must be an Aussie Stock Forums URL'
            }
        )
});

export const CastboxFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('castbox.fm');
            },
            {
                message: 'Must be a Castbox URL'
            }
        )
});

export const ClubhouseFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('clubhouse.com');
            },
            {
                message: 'Must be a Clubhouse URL'
            }
        )
});

export const IHeartRadioFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('iheart.com');
            },
            {
                message: 'Must be an iHeartRadio URL'
            }
        )
});

export const InvestorHubFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('investorhub.com');
            },
            {
                message: 'Must be an InvestorHub URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const PinterestFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('pinterest.com');
            },
            {
                message: 'Must be a Pinterest URL'
            }
        )
});

export const SnapchatFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('snapchat.com');
            },
            {
                message: 'Must be a Snapchat URL'
            }
        )
});

export const SpotifyFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('spotify.com');
            },
            {
                message: 'Must be a Spotify URL'
            }
        )
});

export const StocktwitsFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('stocktwits.com');
            },
            {
                message: 'Must be a Stocktwits URL'
            }
        )
});

export const StrawmanFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('strawman.com');
            },
            {
                message: 'Must be a Strawman URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const TradingQnAFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tradingqna.com');
            },
            {
                message: 'Must be a TradingQnA URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const TumblrFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tumblr.com');
            },
            {
                message: 'Must be a Tumblr URL'
            }
        )
});

export const VimeoFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('vimeo.com');
            },
            {
                message: 'Must be a Vimeo URL'
            }
        )
});

export const WeChatFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('wechat.com');
            },
            {
                message: 'Must be a WeChat URL'
            }
        )
});

export const WhatsAppFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whatsapp.com');
            },
            {
                message: 'Must be a WhatsApp URL'
            }
        )
});

export const WhirlpoolFinanceFormData = BaseActivityFormDataWithAuthor.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whirlpool.net.au');
            },
            {
                message: 'Must be a Whirlpool Finance URL'
            }
        )
});

export const BogleheadsFormData = BaseActivityFormDataWithAuthor.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('bogleheads.org');
            },
            {
                message: 'Must be a Bogleheads URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const ActivityFormData = z.discriminatedUnion('source', [
    TwitterFormData.extend({ source: z.literal('tweet') }),
    HotCopperFormData.extend({ source: z.literal('hotcopper') }),
    LinkedInFormData.extend({ source: z.literal('linkedIn') }),
    RedditFormData.extend({ source: z.literal('reddit') }),
    FacebookFormData.extend({ source: z.literal('facebook') }),
    InstagramFormData.extend({ source: z.literal('instagram') }),
    YouTubeFormData.extend({ source: z.literal('youtube') }),
    TikTokFormData.extend({ source: z.literal('tiktok') }),
    DiscordFormData.extend({ source: z.literal('discord') }),
    TelegramFormData.extend({ source: z.literal('telegram') }),
    MediumFormData.extend({ source: z.literal('medium') }),
    QuoraFormData.extend({ source: z.literal('quora') }),
    SlackFormData.extend({ source: z.literal('slack') }),
    ASXFormData.extend({ source: z.literal('asx-announcement') }),
    ADVFNFormData.extend({ source: z.literal('advfn') }),
    ApplePodcastFormData.extend({ source: z.literal('applePodcast') }),
    AudibleFormData.extend({ source: z.literal('audible') }),
    AussieStockForumsFormData.extend({
        source: z.literal('aussiestockforums')
    }),
    CastboxFormData.extend({ source: z.literal('castbox') }),
    ClubhouseFormData.extend({ source: z.literal('clubhouse') }),
    IHeartRadioFormData.extend({ source: z.literal('iheartradio') }),
    InvestorHubFormData.extend({ source: z.literal('investorhub') }),
    PinterestFormData.extend({ source: z.literal('pinterest') }),
    SnapchatFormData.extend({ source: z.literal('snapchat') }),
    SpotifyFormData.extend({ source: z.literal('spotify') }),
    StocktwitsFormData.extend({ source: z.literal('stocktwits') }),
    StrawmanFormData.extend({ source: z.literal('strawman') }),
    TradingQnAFormData.extend({ source: z.literal('tradingqna') }),
    TumblrFormData.extend({ source: z.literal('tumblr') }),
    VimeoFormData.extend({ source: z.literal('vimeo') }),
    WeChatFormData.extend({ source: z.literal('wechat') }),
    WhatsAppFormData.extend({ source: z.literal('whatsapp') }),
    WhirlpoolFinanceFormData.extend({
        source: z.literal('whirlpoolfinance')
    }),
    BogleheadsFormData.extend({ source: z.literal('bogleheads') }),
    CallFormData.extend({ source: z.literal('call') }),
    MeetingFormData.extend({ source: z.literal('meeting') }),
    PresentationFormData.extend({ source: z.literal('presentation') }),
    EventFormData.extend({ source: z.literal('event') }),
    OtherFormData.extend({ source: z.literal('other') }),
    MediaFormData.extend({ source: z.literal('media') })
]);

export type BaseActivityFormData = z.infer<typeof BaseActivityFormDataWithAuthor>;
export type TwitterFormData = z.infer<typeof TwitterFormData>;
export type HotCopperFormData = z.infer<typeof HotCopperFormData>;
export type LinkedInFormData = z.infer<typeof LinkedInFormData>;
export type RedditFormData = z.infer<typeof RedditFormData>;
export type MediaFormData = z.infer<typeof MediaFormData>;
export type ASXFormData = z.infer<typeof ASXFormData>;
export type FacebookFormData = z.infer<typeof FacebookFormData>;
export type InstagramFormData = z.infer<typeof InstagramFormData>;
export type YouTubeFormData = z.infer<typeof YouTubeFormData>;
export type TikTokFormData = z.infer<typeof TikTokFormData>;
export type DiscordFormData = z.infer<typeof DiscordFormData>;
export type TelegramFormData = z.infer<typeof TelegramFormData>;
export type MediumFormData = z.infer<typeof MediumFormData>;
export type QuoraFormData = z.infer<typeof QuoraFormData>;
export type SlackFormData = z.infer<typeof SlackFormData>;
export type ADVFNFormData = z.infer<typeof ADVFNFormData>;
export type ApplePodcastFormData = z.infer<typeof ApplePodcastFormData>;
export type AudibleFormData = z.infer<typeof AudibleFormData>;
export type AussieStockForumsFormData = z.infer<typeof AussieStockForumsFormData>;
export type CastboxFormData = z.infer<typeof CastboxFormData>;
export type ClubhouseFormData = z.infer<typeof ClubhouseFormData>;
export type IHeartRadioFormData = z.infer<typeof IHeartRadioFormData>;
export type InvestorHubFormData = z.infer<typeof InvestorHubFormData>;
export type PinterestFormData = z.infer<typeof PinterestFormData>;
export type SnapchatFormData = z.infer<typeof SnapchatFormData>;
export type SpotifyFormData = z.infer<typeof SpotifyFormData>;
export type StocktwitsFormData = z.infer<typeof StocktwitsFormData>;
export type StrawmanFormData = z.infer<typeof StrawmanFormData>;
export type TradingQnAFormData = z.infer<typeof TradingQnAFormData>;
export type TumblrFormData = z.infer<typeof TumblrFormData>;
export type VimeoFormData = z.infer<typeof VimeoFormData>;
export type WeChatFormData = z.infer<typeof WeChatFormData>;
export type WhatsAppFormData = z.infer<typeof WhatsAppFormData>;
export type WhirlpoolFinanceFormData = z.infer<typeof WhirlpoolFinanceFormData>;
export type BogleheadsFormData = z.infer<typeof BogleheadsFormData>;
export type CallFormData = z.infer<typeof CallFormData>;
export type MeetingFormData = z.infer<typeof MeetingFormData>;
export type PresentationFormData = z.infer<typeof PresentationFormData>;
export type EventFormData = z.infer<typeof EventFormData>;
export type OtherFormData = z.infer<typeof OtherFormData>;
export type ActivityFormData = z.infer<typeof ActivityFormData>;
