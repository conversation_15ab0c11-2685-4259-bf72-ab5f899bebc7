import { z } from 'zod';
import { activitySource, author } from '../Activity2.js';

const BaseAuthorFormSchema = author.extend({
    source: activitySource,
    userId: z.string().min(1, 'username is required'),
    key: z.string().optional(),
    image: z.string().url().or(z.literal('')).optional(),
    followers: z
        .number()
        .min(0, 'Following must be greater than or equal to 0')
        .nullable()
        .optional(),
    following: z
        .number()
        .min(0, 'Following must be greater than or equal to 0')
        .nullable()
        .optional(),

    url: z.string().url().nullable().optional()
});

const TwitterAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('twitter.com') || url.includes('x.com');
        },
        {
            message: 'Must be a Twitter URL'
        }
    )
});

const HotCopperAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('hotcopper.com.au');
        },
        {
            message: 'Must be a HotCopper URL'
        }
    )
});

const LinkedInAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('linkedin.com');
            },
            {
                message: 'Must be a LinkedIn URL'
            }
        )
});

const RedditAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('reddit.com');
        },
        {
            message: 'Must be a Reddit URL'
        }
    )
});

const FacebookAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('facebook.com');
            },
            {
                message: 'Must be a Facebook URL'
            }
        )
});

const InstagramAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('instagram.com');
            },
            {
                message: 'Must be an Instagram URL'
            }
        )
});

const YouTubeAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('youtube.com') || url.includes('youtu.be');
            },
            {
                message: 'Must be a YouTube URL'
            }
        )
});

const TikTokAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tiktok.com');
            },
            {
                message: 'Must be a TikTok URL'
            }
        )
});

const DiscordAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('discord.com') || url.includes('discord.gg');
            },
            {
                message: 'Must be a Discord URL'
            }
        )
});

const TelegramAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('t.me') || url.includes('telegram.me');
            },
            {
                message: 'Must be a Telegram URL'
            }
        )
});

const MediumAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('medium.com');
            },
            {
                message: 'Must be a Medium URL'
            }
        )
});

const QuoraAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('quora.com');
            },
            {
                message: 'Must be a Quora URL'
            }
        )
});

const SlackAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('slack.com');
            },
            {
                message: 'Must be a Slack URL'
            }
        )
});

const ADVFNAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('advfn.com');
            },
            {
                message: 'Must be an ADVFN URL'
            }
        )
});

const ApplePodcastAuthorFormSchema = BaseAuthorFormSchema.extend({});

const AussieStockForumsAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('aussiestockforums.com');
            },
            {
                message: 'Must be an Aussie Stock Forums URL'
            }
        )
});

const CastboxAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('castbox.fm');
            },
            {
                message: 'Must be a Castbox URL'
            }
        )
});

const ClubhouseAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('clubhouse.com');
            },
            {
                message: 'Must be a Clubhouse URL'
            }
        )
});

const IHeartRadioAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('iheart.com');
            },
            {
                message: 'Must be an iHeartRadio URL'
            }
        )
});

const InvestorHubAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('investorshub.com');
            },
            {
                message: 'Must be an InvestorHub URL'
            }
        )
});

const PinterestAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('pinterest.com');
            },
            {
                message: 'Must be a Pinterest URL'
            }
        )
});

const SnapchatAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('snapchat.com');
            },
            {
                message: 'Must be a Snapchat URL'
            }
        )
});

const SpotifyAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('spotify.com');
            },
            {
                message: 'Must be a Spotify URL'
            }
        )
});

const StocktwitsAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('stocktwits.com');
            },
            {
                message: 'Must be a Stocktwits URL'
            }
        )
});

const StrawmanAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('strawman.com');
            },
            {
                message: 'Must be a Strawman URL'
            }
        )
});

const TradingQnAAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tradingqna.com');
            },
            {
                message: 'Must be a TradingQnA URL'
            }
        )
});

const TumblrAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tumblr.com');
            },
            {
                message: 'Must be a Tumblr URL'
            }
        )
});

const VimeoAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('vimeo.com');
            },
            {
                message: 'Must be a Vimeo URL'
            }
        )
});

const WeChatAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('wechat.com');
            },
            {
                message: 'Must be a WeChat URL'
            }
        )
});

const WhatsAppAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whatsapp.com');
            },
            {
                message: 'Must be a WhatsApp URL'
            }
        )
});

const WhirlpoolFinanceAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whirlpool.net.au');
            },
            {
                message: 'Must be a Whirlpool Finance URL'
            }
        )
});

const BogleheadsAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('bogleheads.org');
            },
            {
                message: 'Must be a Bogleheads URL'
            }
        )
});

const asxAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('asx.com.au');
            },
            {
                message: 'Must be an ASX URL'
            }
        )
});

const AudibleAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('audible.com') || url.includes('audible.com.au');
            },
            {
                message: 'Must be an Audible URL'
            }
        )
});

const OtherAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().optional()
});

export const AuthorFormData = z.discriminatedUnion('source', [
    BaseAuthorFormSchema.merge(TwitterAuthorFormSchema).extend({
        source: z.literal('tweet')
    }),
    BaseAuthorFormSchema.merge(HotCopperAuthorFormSchema).extend({
        source: z.literal('hotcopper')
    }),
    BaseAuthorFormSchema.merge(LinkedInAuthorFormSchema).extend({
        source: z.literal('linkedIn')
    }),
    BaseAuthorFormSchema.merge(RedditAuthorFormSchema).extend({
        source: z.literal('reddit')
    }),
    BaseAuthorFormSchema.merge(FacebookAuthorFormSchema).extend({
        source: z.literal('facebook')
    }),
    BaseAuthorFormSchema.merge(InstagramAuthorFormSchema).extend({
        source: z.literal('instagram')
    }),
    BaseAuthorFormSchema.merge(YouTubeAuthorFormSchema).extend({
        source: z.literal('youtube')
    }),
    BaseAuthorFormSchema.merge(TikTokAuthorFormSchema).extend({
        source: z.literal('tiktok')
    }),
    BaseAuthorFormSchema.merge(DiscordAuthorFormSchema).extend({
        source: z.literal('discord')
    }),
    BaseAuthorFormSchema.merge(TelegramAuthorFormSchema).extend({
        source: z.literal('telegram')
    }),
    BaseAuthorFormSchema.merge(MediumAuthorFormSchema).extend({
        source: z.literal('medium')
    }),
    BaseAuthorFormSchema.merge(QuoraAuthorFormSchema).extend({
        source: z.literal('quora')
    }),
    BaseAuthorFormSchema.merge(SlackAuthorFormSchema).extend({
        source: z.literal('slack')
    }),
    BaseAuthorFormSchema.merge(ADVFNAuthorFormSchema).extend({
        source: z.literal('advfn')
    }),
    BaseAuthorFormSchema.merge(ApplePodcastAuthorFormSchema).extend({
        source: z.literal('applePodcast')
    }),
    BaseAuthorFormSchema.merge(AudibleAuthorFormSchema).extend({
        source: z.literal('audible')
    }),
    BaseAuthorFormSchema.merge(AussieStockForumsAuthorFormSchema).extend({
        source: z.literal('aussiestockforums')
    }),
    BaseAuthorFormSchema.merge(CastboxAuthorFormSchema).extend({
        source: z.literal('castbox')
    }),
    BaseAuthorFormSchema.merge(ClubhouseAuthorFormSchema).extend({
        source: z.literal('clubhouse')
    }),
    BaseAuthorFormSchema.merge(IHeartRadioAuthorFormSchema).extend({
        source: z.literal('iheartradio')
    }),
    BaseAuthorFormSchema.merge(InvestorHubAuthorFormSchema).extend({
        source: z.literal('investorhub')
    }),
    BaseAuthorFormSchema.merge(PinterestAuthorFormSchema).extend({
        source: z.literal('pinterest')
    }),
    BaseAuthorFormSchema.merge(SnapchatAuthorFormSchema).extend({
        source: z.literal('snapchat')
    }),
    BaseAuthorFormSchema.merge(SpotifyAuthorFormSchema).extend({
        source: z.literal('spotify')
    }),
    BaseAuthorFormSchema.merge(StocktwitsAuthorFormSchema).extend({
        source: z.literal('stocktwits')
    }),
    BaseAuthorFormSchema.merge(StrawmanAuthorFormSchema).extend({
        source: z.literal('strawman')
    }),
    BaseAuthorFormSchema.merge(TradingQnAAuthorFormSchema).extend({
        source: z.literal('tradingqna')
    }),
    BaseAuthorFormSchema.merge(TumblrAuthorFormSchema).extend({
        source: z.literal('tumblr')
    }),
    BaseAuthorFormSchema.merge(VimeoAuthorFormSchema).extend({
        source: z.literal('vimeo')
    }),
    BaseAuthorFormSchema.merge(WeChatAuthorFormSchema).extend({
        source: z.literal('wechat')
    }),
    BaseAuthorFormSchema.merge(WhatsAppAuthorFormSchema).extend({
        source: z.literal('whatsapp')
    }),
    BaseAuthorFormSchema.merge(WhirlpoolFinanceAuthorFormSchema).extend({
        source: z.literal('whirlpoolfinance')
    }),
    BaseAuthorFormSchema.merge(BogleheadsAuthorFormSchema).extend({
        source: z.literal('bogleheads')
    }),
    BaseAuthorFormSchema.merge(asxAuthorFormSchema).extend({
        source: z.literal('asxasx-announcement')
    }),
    BaseAuthorFormSchema.merge(OtherAuthorFormSchema).extend({
        source: z.literal('other')
    })
]);

export type AuthorFormData = z.infer<typeof AuthorFormData>;
